---
layout: mypost
title: 使用精灵图完成推特点赞红心效果
categories: [前端, CSS]
---

css 有一项技术叫做 **sprite sheets** ，中文翻译过来就是**精灵图**，现代前端开发中，现在已经很少还有在用了，主要原因还是在一些页面容易模糊，另外也不容易维护，不过优点就是可以制作一些动画，可以减少 http 请求等，更详细的概念可以谷歌了解。

## 效果展示

<iframe src="/demos/twitter-redHeart.html" width="100%" height="200px" frameborder="0"></iframe>

## 实现原理

主要是利用了精灵图可以借助 `background-size` 和 `background-position` 进行图片的切割，再配合 `@keyframes` 就可以实现一个动画了。

## 代码实现

### 准备一张精灵图

![精灵图](01.png)

### HTML

准备一个容器

```html
<div class="redHeart"></div>
```

### CSS

我们可以先看没有动画效果的，利用 `background-position` 实现切割图片

```css
.redHeart {
  background-image: url('../posts/2025/01/25/01.png');
  width: 100px;
  height: 100px;
  /* 这里设置 2900px 的缘故是因为精灵图，分割的话有 29 张小图 */
  background-size: 2900px 100px;
  /* 精灵图中的每张小图宽度是 100px ，这样就相当于移动到了第六张图 */
  background-position: -500px 0px;
}
```

大概是下面的效果：

![效果展示](02.png)

现在可以加上动画来看看

```css
 .redHeart {
        background-image: url('../posts/2025/01/25/01.png');
        width: 100px;
        height: 100px;
        background-size: 2900px 100px;
        animation: redHeart 2s steps(29, jump-none) infinite;
      }
      @keyframes redHeart {
        0% {
          /* 第一张小图 */
          background-position: 0px 0px;
        }
        100% {
          /* 最后一张小图，负代表左滑动 */
          background-position: -2800px 0px;
        }
      }
```
重要提示：注意上面动画规则中的steps()计时函数！这是过渡精确落在帧上所必需的。可以自己在页面上修改下 steps 的值来看下效果


这样就可以实现上面的效果了
