---
layout: mypost
title: babel 使用
categories: [教程]
---


# 什么是 Babel

Babel 是一个 JavaScript 编译器,提供了JavaScript的编译过程，能够将源代码转换为目标代码。

AST -> Transform -> Generate

官网 https://babeljs.io/

查看AST https://astexplorer.net/

Babel所有的包 https://babeljs.io/docs/babel-traverse

## 核心功能
1. 语法转换：将新版本的 JavaScript 语法转换为旧版本的语法
2. Polyfill：通过引入额外的代码，使新功能在旧浏览器中可用
3. JSX: 将JSX语法转换成普通的JavaScript语法
4. 插件: 为Babel提供自定义功能

# Babel 插件开发示例

本[项目](https://github.com/slince-zero/babel-exercise)包含了多个 Babel 使用示例，包括自定义插件开发、ES6 转 ES5、以及 JSX 转换。通过这些示例，展示了 Babel 在代码转换方面的强大功能。

## 项目结构

- `index-plugin.js`: 箭头函数转换插件实现文件
- `test-plugin.js`: 包含待转换箭头函数的测试文件
- `index.js`: ES6 转 ES5 示例文件
- `index-jsx.js`: JSX 转换示例文件
- `test.js`: ES6 测试代码
- `test.jsx`: JSX 测试代码

## 功能示例

### 1. ES6 转 ES5（index.js）

使用 `@babel/preset-env` 将 ES6+ 代码转换为 ES5，支持按需 polyfill：

```javascript
import Babel from '@babel/core'
import presetEnv from '@babel/preset-env'

const res = Babel.transform(file, {
  presets: [
    [
      presetEnv,
      {
        // usage: 按需引入 polyfill
        // entry: 手动引入全部 polyfill
        useBuiltIns: 'usage',
        // 使用 core-js 3.x 版本进行 polyfill
        corejs: 3,
      },
    ],
  ],
})
```

### 2. JSX 转换（index-jsx.js）

使用 `@babel/preset-react` 将 JSX 代码转换为普通 JavaScript：

```javascript
import Babel from '@babel/core'
import presetEnv from '@babel/preset-env'
import react from '@babel/preset-react'

const res = Babel.transform(jsxFile, {
  presets: [
    [presetEnv, { useBuiltIns: 'usage', corejs: 3 }],
    react
  ],
})
```

### 3. 自定义箭头函数转换插件

本项目的插件演示了以下功能：

1. **访问者模式**: 使用 Babel 的访问者模式来遍历和转换 AST 节点
2. **AST 转换**: 将箭头函数（`=>`）转换为常规函数表达式
3. **类型检查**: 使用 Babel 的 `types` 工具进行 AST 节点操作

```javascript
// 转换示例:
// 输入:
const fn = (param) => param + 10;

// 输出:
const fn = function(param) {
    return param + 10;
};
```

## 安装依赖

```bash
npm install @babel/core @babel/preset-env @babel/preset-react core-js@3
```

## 使用方法

1. ES6 转换:
```bash
node index.js
```

2. JSX 转换:
```bash
node index-jsx.js
```

3. 箭头函数转换插件:
```bash
node index-plugin.js
```

## Babel 配置说明

### preset-env 配置

- **useBuiltIns**: 控制 polyfill 的引入方式
  - `usage`: 按需引入，根据代码中使用的特性自动引入所需的 polyfill
  - `entry`: 手动引入全部 polyfill
- **corejs**: 指定使用的 core-js 版本，推荐使用版本 3

### preset-react 配置

- 用于转换 JSX 语法
- 可以与 preset-env 配合使用，确保完整的浏览器兼容性

## 核心概念

1. **AST 访问器**: 插件使用访问者模式来匹配和转换 `ArrowFunctionExpression` 节点
2. **节点类型**: 使用 Babel 的类型工具（`t.blockStatement`, `t.functionExpression` 等）
3. **路径操作**: 使用 `path.replaceWith()` 来替换 AST 中的节点

## 技术细节

转换过程处理以下内容：
- 函数参数
- 函数体（包括块级语句和表达式形式）
- 异步箭头函数
- return 语句

这个示例展示了 Babel 转换功能的强大之处，以及如何实现自定义的 JavaScript 语法转换。 