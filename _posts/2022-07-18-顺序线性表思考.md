---
layout: mypost
title: 顺序线性表思考
categories: [数据结构]
---

首先看顺序表的定义：

>  一组地址连续的**存储**单元，顺序存储线性表中的**数据**单元，使得**逻辑**上两个相邻的元素在**物理位置**上也相邻。

这里就有一个疑问了，数组和顺序表又有什么关系？

1. 顺序表在计算机内以数组形式保存。
2. 线性表是从逻辑角度来看待的，它除了首和尾，其他每一个元素都有一个前驱和后继元素。
3. 数组则是从物理贮存角度来看待的，不仅顺序表可以用数组来存储，队列和栈也可以。
4. 顺序表和数组都是数据结构，只是描述角度不同，不过数组是一个更大的概念。

接下来，我们看代码：

    typedef struct {
    int data[MaxSize];
    int length;
	} SqList;
	//初始化线性表
	void InitList(SqList &L){
	    for (int i = 0; i < MaxSize; ++i) {
	        L.data[i] =0;
	    }
	    L.length=0;  //空表
	}

因为我要执行插入和删除操作，所以我先定义了一个空表，但随后我执行插入和删除操作的时候，却一直失败，我反复检查插入和删除代码，发现并无问题。

经过一番思考查找之后，我发现问题出在 L.length = 0上面，是的，我建立了一个空表，我在表里面添加数据的时候，并没有对L.length进行++操作。所以使得我的输出结果始终得不到我想要的。

好在，最后解决！

附上完整代码：

 
	#include "stdio.h"
	#include "stdlib.h"
	#define MaxSize 10
	
	typedef struct {
	    int data[MaxSize];
	    int length;
	} SqList;
	//初始化线性表
	void InitList(SqList &L){
	    for (int i = 0; i < MaxSize; ++i) {
	        L.data[i] =0;
	    }
	    L.length=9;    //这里初始化应该为0才对，由于为了数据测试方便，因此设为9
	}
	
	//插入操作
	bool ListInsert (SqList &L, int i, int e){
	    if (i < 1 || i > L.length) {                //判断i的范围是否正确
	        return false;
	    }
	    if (L.length >= MaxSize) {                  //判断空间是否已满
	        return false;
	    }
	    for (int j = L.length; j >= i; j--) {       //讲第i个元素及之后元素后移
	        L.data[j] = L.data[j-1];
	    }
	    L.data[i-1] = e;                            //讲元素e赋给第i-1（数组下标从0开始）个位置
	    L.length++;
	    return true;
	}
	
	//删除操作
	bool ListDelete(SqList &L, int i, int &e){
	    if(i<1||i>L.length){
	        return false;
	    }
	    e=L.data[i-1];                  //将被删除元素赋给e
	    for (int j = i; j < L.length; j++) {
	        L.data[j-1]=L.data[j];      //将第i个位置之后的元素前移
	    }
	    L.length--;
	    return true;
	
	}
	
	
	int main(){
	    printf("hello world\n");
	    SqList L;
	    //初始化
	    InitList(L);
	    for(int i =0; i<MaxSize;i++){
	        printf("data[%d]=%d\n",i,L.data[i]);
	    }
	    for (int i = 0; i < 8; ++i) {
	        L.data[i] = i;
	//        L.length++;
	        printf("data[%d]=%d\n",i,L.data[i]);
	    }
	    printf("******************************\n");
	    printf("L.leng=%d\n",L.length);
	
    //插入
    bool flagIn = ListInsert(L,2,33);
    printf("flag:%d\n",flagIn);
    for(int i =0; i<MaxSize;i++){
        printf("data[%d]=%d\n",i,L.data[i]);
    }
    printf("******************************\n");

    //删除
    int e = -1;
    bool flagDel = ListDelete(L,2,e);
    printf("falg:%d\n",flagDel);
    for(int i =0; i<MaxSize;i++){
        printf("data[%d]=%d\n",i,L.data[i]);
    }
    printf("删除的元素是%d\n",e);
    printf("******************************\n");
    return 0;
}