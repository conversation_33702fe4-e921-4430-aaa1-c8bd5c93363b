---
layout: mypost
title: 少年泪
categories: [story]
---


今天是 2024 年的 8 月 15 日，距离六月份已经过去了两个多月了，本来标题想取‘年中总结’的，不过现在好像也不是年中了。所以，想来想去，想不到好的标题，恰好听到了斗破苍穹动漫的插曲，《少年泪》，音乐很好听，动漫也有很多触动我的地方。

动漫中最喜欢的地方，大概就是主角一路走来，磕磕绊绊，且走且战，虽然九死一生，但还是坚持了下来。这里面有一个篇章是三年之约，是这部作品一个十分重要的章节，动漫制作组，将这段故事还原的也十分精彩，推荐大家去观看。

说回到标题上面，‘少年泪’ 简单理解就是，少年在哭泣，我不知道自己还是否算是少年，我今年已经 24 岁了，好像应该算是青年了。不过，自己这一路走来实在是太不容易了，虽然和朋友聊起来的时候，自己总是嘻嘻哈哈的，但是实际怎么样，也只有自己知道。5月末的时候，写过一篇文章 [节流-防抖以及这段时间来的感受](https://slince-zero.github.io/posts/2024/05/30/节流-防抖以及这段时间来的感受.html)，虽然里面介绍一些关于节流和防抖的概念，但写那篇文章，主要还是发泄一些情绪。当然，并没有一些奇怪的言论，只是很普通的叙述一些事情。

此时此刻，我已经离开了北京，来到了深圳，这里的温度空气和北京差别很大，不过我很喜欢这边的空气，比较湿润，氧气吸进肺部感到很舒服，这让我这个北方人很欣慰。

是的，我找到一份新的工作，下周就要入职了，我很喜欢这家公司，我使用他们家的产品已经很多年了，没想到自己有机会成为自己喜欢产品的开发者，这对我来说，是一件十分幸运和有挑战的事情。

接到 offer 的那一瞬间，我的情绪出奇的稳定和冷静，女朋友在旁边也很替我高兴，她甚至比我还要激动，她说，“才不是幸运，这就是你努力的结果。” 是这样吗？我不知道，只是深深地吸了一口气，轻轻地吐了出去。

回想这大半年来，也是有很多有意思的地方，第一次去从来没有听过，而且很偏很偏的地方参加面试；完成了第一个从零到一的开源项目，幸运的得到了一些关注，甚至还有投资者发来邮件，虽然最后并没有通过评审，不过还是很感谢；跟着女朋友学到了很多菜的做法，好像现在做素菜我已经强过她了，虽然她不承认，但是，who care！当然还有很多，也有许多心酸的时候，求职期间屡屡碰壁，一度怀疑自己，躲在出租屋里偷偷地哭，不过好在坚持了下来，没有自暴自弃，我开始向全国，甚至向全世界投递简历，很幸运的是，有些国内和国外的公司回复了我，虽然邮件当中，他们说明了我的个人情况并不是很适合他们的团队，但是，这给了我启发，我知道了自己还有哪些不足的地方，需要补充和完善。

其中，这些邮件当中，最没有想到的就是 GitHub 的创始人（Tom Preston-Werner），回复了我的邮件，我十分的震惊，真的没有想到他竟然真的能够回复我，这简直不可思议，我在邮件中写道，现在求职环境似乎不是很好，想寻求一些关于求职的建议，他回复我说，可以参与一些开源项目，成为坚实的开源项目维护者。

这对我也有了一些新的启发，原本我很多学习的内容就是从一些开源项目中学到的，所以，后面我在自己课余的时间，仍然会写自己的开源项目，每次打开一些开源项目的时候，看着前辈的代码风格，和严谨的逻辑，感叹，写的真好，就是不是自己写的。

这一路走来，经历了很多，虽然说不上自己有多少成长，但是还算欣慰，至少自己并没有荒废时间，接下来的日子里，就该开启下一个故事了。虽然我不知道，未来会发生什么，但希望会向着好的方向发展。我逐渐意识到一点，就是我们会对未知的事情充满恐惧，或者对没有发生的事情，感到迷茫，我想说的是，让它顺其自然，如果发生了，那么就坦然接受，如果还没有发生，那就做好你能做好的准备。

虽然现在大环境不好，我对未来仍然充满希望，我所说的希望，不是我未来能做成什么大事业，只是很简单的一点，我想要做自己的事情，我喜欢编程，虽然我的代码水平不是很高，但我还是很喜欢写下代码的那一刻，逻辑正确，项目跑通的那一瞬间的喜悦。

所以，别怕，想哭的时候，就哭一哭吧，哭完了对自己也是一种释放，虽然很难，但生活还是得照常过，我现在仍然有很大的压力，仍然有很多事情需要我去一件一件的去完成，去解决。

感谢这段时间以来遇到的所有人，希望你们在未来的日子里都能够有所收获。

感谢我的女朋友女朋友！

最后，希望我女朋友的身体变得更好一些。
