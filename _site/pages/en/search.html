<!DOCTYPE html>
<html lang="en">
<head>
  <title>Search-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="slince的个人博客"/>
  <meta name="keywords" content="slince,Blog,Java,Html,JavaScript"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/page.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="zh-CN" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-search">
  <h1>
    <span>Search</span>
    <img src="/static/img/loading.svg">
  </h1>
  <input id="search-input" type="text" placeholder="Enter keywords to search..." />
  <div class="list-search">
  </div>
</div>

<script>
  // 加载搜索数据
  window.searchData = [];
  var xhr = new XMLHttpRequest();
  xhr.open('GET', '/static/xml/search.xml?t=' + new Date().getTime());
  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && xhr.status === 200) {
      var xml = xhr.responseXML;
      var entries = xml.getElementsByTagName('entry');
      for (var i = 0; i < entries.length; i++) {
        var entry = entries[i];
        var title = entry.getElementsByTagName('title')[0].textContent;
        var content = entry.getElementsByTagName('content')[0].textContent;
        var url = entry.getElementsByTagName('url')[0].textContent;
        var lang = entry.getElementsByTagName('lang')[0]?.textContent || 'zh-CN';
        if (lang === currentLang) {
          searchData.push({
            title: title,
            content: content,
            url: url,
            lang: lang
          });
        }
      }
    }
  };
  xhr.send();

  // 处理搜索
  function search(keyword) {
    if (keyword.length === 0) {
      document.querySelector('.list-search').innerHTML = '';
      return;
    }
    keyword = keyword.toLowerCase();
    var results = [];
    for (var i = 0; i < searchData.length; i++) {
      var item = searchData[i];
      var title = item.title.toLowerCase();
      var content = item.content.toLowerCase();
      if (title.indexOf(keyword) > -1 || content.indexOf(keyword) > -1) {
        results.push(item);
      }
    }
    var html = '';
    if (results.length > 0) {
      for (var i = 0; i < results.length; i++) {
        var item = results[i];
        var title = item.title.replace(new RegExp(keyword, 'gi'), function (match) {
          return '<span class="hint">' + match + '</span>';
        });
        var content = item.content.replace(new RegExp(keyword, 'gi'), function (match) {
          return '<span class="hint">' + match + '</span>';
        });
        html += '<li>';
        html += '<a href="' + item.url + '">';
        html += '<span class="title">' + title + '</span>';
        html += '<span class="content">' + content + '</span>';
        html += '</a></li>';
      }
    } else {
      html = '<li><span class="content">No results found</span></li>';
    }
    document.querySelector('.list-search').innerHTML = html;
  }

  // 监听输入
  document.getElementById('search-input').addEventListener('input', function () {
    search(this.value.trim());
  });
</script> 
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>
</body>
</html>
