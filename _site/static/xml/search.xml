<?xml version="1.0" encoding="utf-8"?>
<ul>
  <li>YoungWomanandtheSea中文译名泳者之心，讲述了那个女性运动不受关注的年代，对女性充满了傲慢与偏见，女主TrudyEderle虽然活在这样的社会当中，但始终热爱着游泳运动，并成功缔造了历史上首位横渡英吉利海峡女泳者的传奇。不知从什么时候起，我开始逐渐适应这个短视频，且快节奏的时代，什么都快，工作要快，出餐要快，视频要快，爱情要快，仿佛什么都是快的；视频有倍速播放，还有视频讲解，一个又一个快速讲解的故事，本来需要花费数个小时，甚至是数天，数月才能看完看懂的故事，却是被三两小时就就被‘消化殆尽’。我有太久，没有停下来好好的看一场电影，慢节奏的思考，慢节奏的享受，泳者之心，这部电影，带给我的不仅仅是感动，更多的是思考，曾几何时，我曾这样问过自己，自己到底是谁，为什么出生在这个世上，又在追求着什么。看完这部影片，这几个问题，我仍然没有答案，但是它却让我重新回想起这几个问题。是啊，孩童或者说，少年时代，我就是这样过来的，好像一切都很慢，那会还天真的想快些长大，因为长大了就可以一个人出去旅行，去好多好多国家，认识好多好多人。可随着时代的发展，随着年龄的增长，渐渐地意识到，少年时候的自己，对这个世界的认知还是太浅显了，不是长大了，就可以去好多地方，不是长大了，就可以认识好多人，也不是长大了，就可以做到以前做不到的时候，也许这一生都无法涉足……看完这个电影，我只是想写些什么，并没有什么主题，或许只是我一个人的言言语语，一些牢骚而已。回想起今年上半年发生过的事情，平淡且无味，但回想起来，好像什么都没有做，我感到非常的可怕；年前，我找到一份新的工作，赶在过年之前入职，之后就是不停地在工作，年后依然很是忙碌，不过好在每天过的很充实，每天都有新的任务去完成。我从二月中旬，开始记录自己每天都做了些什么（周末除外，不过周末偶尔也会加班），现在看看，这确确实实是一个很好的决定，它除了帮我记录自己完成了哪些工作，能帮我快速完成周报总结，也帮我回顾一些历史问题，但我总感觉缺少了什么。好像大家都在一条笔直或弯曲的公路上不停地奔跑着，有的人，运动装备齐全，跑的很快，有的人开着小汽车飞驰着，也有的人衣着普通，但是总是会停下脚步来，看看这看看那的；也有人衣着破破烂烂的，但是一直在慢慢地小跑，好像谁也不能阻止他停下脚步。我感觉孩童时代的自己，属于第四种人，中学时代，属于第一种人，现在的我是第三种人，我也不清楚是什么导致的，但是我清楚得能感觉到自己身上的一些变化。似乎对一切都不那么感兴趣，很多东西，都是三分钟热度，也没有办法停下脚步，认认真真地思考一番。关于这部影片，我不做过多的剧透，我也不喜欢剧透，但是我推荐大家去看一看，希望你看完之后有所思，有所获。静下心来之后，发现，其实这几个月过来还是发生了很多事情的，工作上，从最开始的毛毛躁躁，东问西问的，到现在的，至少交给我一个需求，能够完成，不过偶尔也需要问到我的mentor，在此感谢我的mentor不吝赐教。生活上，就是比较平淡了，工作日就是上班下班，晚上洗澡睡觉。周末就是在家休息，偶尔可能会跟朋友一起出去吃个饭，这样的生活也不错。有时，也会收到来自父母的叨扰，说谁谁家又结婚了，谁谁家又生了个孩子，或许大家或多或少都有过来自父母的念念叨叨。以前觉得就是，你说这些有什么用，现在可能就是，你说，嗯，我听着，也不会刻意去说一些反驳的话语。毕竟，我们在长大，他们也在老去。小学的时候，我的奶奶离开了，初中的时候，爷爷离开了，刚参加工作第一年的时候，姥爷也离开了…那是某天的上午或者下午，天气好或者不好，一些你平时没有注意到的东西，突然触动了你的心弦，然后你的情绪一下子突然像是气球装满了水，不知道被什么给刺破，然后水一涌而下，怎么也没办法将水重新聚拢回去，除非能够时光倒流，除非能够时光倒流。所以啊，珍惜身边的人。这篇文章，内容写的不多，但是希望能够带给你思考。</li>
  <li>css有一项技术叫做spritesheets，中文翻译过来就是精灵图，现代前端开发中，现在已经很少还有在用了，主要原因还是在一些页面容易模糊，另外也不容易维护，不过优点就是可以制作一些动画，可以减少http请求等，更详细的概念可以谷歌了解。效果展示实现原理主要是利用了精灵图可以借助background-size和background-position进行图片的切割，再配合@keyframes就可以实现一个动画了。代码实现准备一张精灵图HTML准备一个容器&lt;divclass="redHeart"&gt;&lt;/div&gt;CSS我们可以先看没有动画效果的，利用background-position实现切割图片.redHeart{background-image:url('../posts/2025/01/25/01.png');width:100px;height:100px;/*这里设置2900px的缘故是因为精灵图，分割的话有29张小图*/background-size:2900px100px;/*精灵图中的每张小图宽度是100px，这样就相当于移动到了第六张图*/background-position:-500px0px;}大概是下面的效果：现在可以加上动画来看看.redHeart{background-image:url('../posts/2025/01/25/01.png');width:100px;height:100px;background-size:2900px100px;animation:redHeart2ssteps(29,jump-none)infinite;}@keyframesredHeart{0%{/*第一张小图*/background-position:0px0px;}100%{/*最后一张小图，负代表左滑动*/background-position:-2800px0px;}}重要提示：注意上面动画规则中的steps()计时函数！这是过渡精确落在帧上所必需的。可以自己在页面上修改下steps的值来看下效果这样就可以实现上面的效果了</li>
  <li>今天和大家分享一个使用纯CSS实现的无限轮播效果。这个轮播效果不需要任何JavaScript，完全依靠CSS动画来实现，而且支持鼠标悬停暂停。效果展示实现原理实现这个效果的核心在于：创建两组完全相同的内容使用CSSanimation让内容持续向左滚动当第一组内容完全滚出视野时，第二组内容刚好补上，造成无缝衔接的效果代码实现首先是HTML结构：&lt;divclass="carousel"&gt;&lt;divclass="group"&gt;&lt;divclass="card"&gt;A&lt;/div&gt;&lt;divclass="card"&gt;B&lt;/div&gt;&lt;divclass="card"&gt;C&lt;/div&gt;&lt;/div&gt;&lt;!--复制一组相同的内容--&gt;&lt;divclass="group"aria-hidden="true"&gt;&lt;divclass="card"&gt;A&lt;/div&gt;&lt;divclass="card"&gt;B&lt;/div&gt;&lt;divclass="card"&gt;C&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;CSS样式：.carousel{margin:0auto;padding:20px0;max-width:700px;overflow:hidden;display:flex;&gt;*{flex:00100%;}&amp;:hover.group{animation-play-state:paused;cursor:pointer;}}.card{width:100%;color:#000;border-radius:24px;box-shadow:rgba(0,0,0,10%)5px5px20px0;padding:20px;font-size:xx-large;justify-content:center;align-items:center;min-height:200px;&amp;:nth-child(1){background:#7958ff;}&amp;:nth-child(2){background:#7bb265;}&amp;:nth-child(3){background:#d29b9b;}}.group{display:flex;gap:20px;padding-right:20px;will-change:transform;animation:scrolling10slinearinfinite;}@keyframesscrolling{0%{transform:translateX(0);}100%{transform:translateX(-100%);}}关键点解析容器设置使用overflow:hidden隐藏超出部分使用display:flex让两组内容水平排列动画实现使用@keyframes定义动画，从0%位置移动到-100%位置设置animation:scrolling10slinearinfinite使动画无限循环linear确保动画速度均匀性能优化使用will-change:transform提前告知浏览器即将发生变换使用transform而不是改变left或margin属性，可以获得更好的性能交互体验当鼠标悬停时，设置animation-play-state:paused暂停动画添加cursor:pointer提示用户可交互无障碍优化为第二组重复内容添加aria-hidden="true"，避免屏幕阅读器重复读取使用场景这种无限轮播效果适用于：展示产品特性显示用户评价展示合作伙伴logo新闻标题滚动等注意事项动画时长需要根据内容长度和期望的滚动速度来调整内容数量不宜过多，否则会影响性能在移动端需要注意性能问题建议添加媒体查询，在小屏幕设备上调整合适的显示方式总结这个纯CSS实现的无限轮播效果，代码简洁，性能良好，而且不需要依赖任何JavaScript库。通过合理运用CSS动画和变换，我们可以实现很多有趣的效果。希望这个例子能给大家一些启发！</li>
  <li>什么是BabelBabel是一个JavaScript编译器,提供了JavaScript的编译过程，能够将源代码转换为目标代码。AST-&gt;Transform-&gt;Generate官网https://babeljs.io/查看ASThttps://astexplorer.net/Babel所有的包https://babeljs.io/docs/babel-traverse核心功能语法转换：将新版本的JavaScript语法转换为旧版本的语法Polyfill：通过引入额外的代码，使新功能在旧浏览器中可用JSX:将JSX语法转换成普通的JavaScript语法插件:为Babel提供自定义功能Babel插件开发示例本项目包含了多个Babel使用示例，包括自定义插件开发、ES6转ES5、以及JSX转换。通过这些示例，展示了Babel在代码转换方面的强大功能。项目结构index-plugin.js:箭头函数转换插件实现文件test-plugin.js:包含待转换箭头函数的测试文件index.js:ES6转ES5示例文件index-jsx.js:JSX转换示例文件test.js:ES6测试代码test.jsx:JSX测试代码功能示例1.ES6转ES5（index.js）使用@babel/preset-env将ES6+代码转换为ES5，支持按需polyfill：importBabelfrom'@babel/core'importpresetEnvfrom'@babel/preset-env'constres=Babel.transform(file,{presets:[[presetEnv,{//usage:按需引入polyfill//entry:手动引入全部polyfilluseBuiltIns:'usage',//使用core-js3.x版本进行polyfillcorejs:3,},],],})2.JSX转换（index-jsx.js）使用@babel/preset-react将JSX代码转换为普通JavaScript：importBabelfrom'@babel/core'importpresetEnvfrom'@babel/preset-env'importreactfrom'@babel/preset-react'constres=Babel.transform(jsxFile,{presets:[[presetEnv,{useBuiltIns:'usage',corejs:3}],react],})3.自定义箭头函数转换插件本项目的插件演示了以下功能：访问者模式:使用Babel的访问者模式来遍历和转换AST节点AST转换:将箭头函数（=&gt;）转换为常规函数表达式类型检查:使用Babel的types工具进行AST节点操作//转换示例://输入:constfn=(param)=&gt;param+10;//输出:constfn=function(param){returnparam+10;};安装依赖npminstall@babel/core@babel/preset-env@babel/preset-reactcore-js@3使用方法ES6转换:nodeindex.jsJSX转换:nodeindex-jsx.js箭头函数转换插件:nodeindex-plugin.jsBabel配置说明preset-env配置useBuiltIns:控制polyfill的引入方式usage:按需引入，根据代码中使用的特性自动引入所需的polyfillentry:手动引入全部polyfillcorejs:指定使用的core-js版本，推荐使用版本3preset-react配置用于转换JSX语法可以与preset-env配合使用，确保完整的浏览器兼容性核心概念AST访问器:插件使用访问者模式来匹配和转换ArrowFunctionExpression节点节点类型:使用Babel的类型工具（t.blockStatement,t.functionExpression等）路径操作:使用path.replaceWith()来替换AST中的节点技术细节转换过程处理以下内容：函数参数函数体（包括块级语句和表达式形式）异步箭头函数return语句这个示例展示了Babel转换功能的强大之处，以及如何实现自定义的JavaScript语法转换。</li>
  <li>现在博客已经支持中文和英文切换了，点击右上角语言切换按钮即可，24年终总结移动到了英文模块下。前几天，我写了一篇年终总结发到社区里面，受到了很多前辈的鼓励和支持，再次表示非常的感谢。这两周，参加了字节豆包的一个比赛，用AI来写一款软件，虽然没有入选，不过也学到了很多东西，这是提交地址，有兴趣的可以看看。另外就是参加了一些面试，这几场面试给我的感觉都很好，面试官也都很专业，也从他们身上学到了很多有用和值得思考的地方。印象最深的只要是两家公司，一家是用借助AI做一些C端小应用的，他们厉害的地方在于，产品集成了市面上大多数我们经常用到的工具，比如思维导图，图片编辑工具，AI生成头像、图片等。另外一家公司做的是面向B端的商业应用，是面向企业用户帮助他们快速分析数据，依托于AI的能力来快速落地实现相应的定制化需求。我想谈一谈从这两家公司的面试中学到的东西。首先是第一家，面试官问了我一个问题，“你是怎么理解react中的key的”，然后我的回答是，“key是react中用来优化性能的，当我们在使用列表渲染的时候，react会根据key来判断是否需要重新渲染，如果key相同，则不会重新渲染”。乍一听，可能没什么问题，很标准的一个回答，但是面试官却指出了一个点，他说，“你知道具体是怎样做的吗？”我思考了一会，说了一些原理上的东西，不过看他的表情，可能并不是他想听到的，随后他帮我做出了解释，以下是他的一些解释，我做了一定的补充。letkey=1;functionApp(){return&lt;divkey={key}&gt;Hello&lt;/div&gt;;}…做了一些操作，比如当key不变时key=1;====&gt;App()React会复用现有的组件实例，不会重新创建当key改变时key=2;====&gt;App()React会先卸载旧的组件实例，再重新创建组件实例这个机制在实践中非常重要，比如：当我们需要完全重置一个组件的状态时，改变它的key是最简单的方法在列表渲染中，key的变化会导致对应项被视为全新元素，从而触发完整的生命周期这也解释了为什么不应该使用数组索引作为key，因为它可能导致组件状态错乱//✅最佳实践：使用后端返回的唯一IDconsttodoList=todos.map(todo=&gt;(&lt;TodoItemkey={todo.id}{...todo}/&gt;));//✅使用唯一的业务标识constuserList=users.map(user=&gt;(&lt;UserCardkey={user.email}{...user}/&gt;));//❌不要使用数组索引items.map((item,index)=&gt;(&lt;Componentkey={index}/&gt;//可能导致渲染错误和性能问题));//❌不要使用随机值&lt;Componentkey={Math.random()}/&gt;//每次渲染都会创建新组件//❌不要使用不稳定的值&lt;Componentkey={Date.now()}/&gt;//会导致不必要的重渲染其实通过上面的这个简单的问题，我们就可以看到背后的事情并不是那么简单，可能有些时候，我们作为框架的使用者，但也仅仅是作为使用者，并没有深入到背后的原理，并且分析他是如何执行和实现的。另外在和他交流的过程中，也对我学习上提出了一些建议，可以先看一些简单的小工具的源码，比如redux核心代码非常少，然后慢慢地扩展到自己能够看react源码，到最后实现一个minireact。如果能做到这样，其实才能够真正的算是入门了。后面几天，我一方面在思考自己应该怎么做，怎么学习，另一方面也在看redux的源码，写了一遍之后，你会发现，真的是很简单，就那么几行代码就能够实现了，真的很令人吃惊。另外也感叹，redux的作者，能有这么优雅的解决方案，而且代码简洁明了，非常非常令人佩服。自己手写一遍redux核心原理之后，虽然我说不上对它有多么深刻的认识，但是至少我现在对它又有了一个新的理解。我能够感觉到自己，在慢慢地构建着什么。再接下来谈一谈第二家公司，我对最后一面印象很深很深，请允许我用老师来称呼对方，因为对方给我的感觉，就像是一名经验丰富的老师，在给我做一些讲解。我有问到，我对自己此时此刻以及未来的方向上有些困惑，这位老师告诉我说，“你前面提到的原理很重要，当你真正弄明白一些事物背后的原理之后，你会发现其实没什么，虽然这个时代环境并不是很好，但是你也要不断去学习，追寻你真正想要的，这不管是对于你个人还是公司，都是很重要的。”虽然和这位老师聊的不是很多，但是，我回忆起一些高中时候的事情，那会我的语文老师曾教导过我，她说，成为一个像颜回那样的人，不管别人怎么说，也不要改变自己的想法。现在想想，他们这些人都是始终在做自己感兴趣的事情，并且坚持下去。我对自己的要求从来不是很高，但是只要是认定了一件事，那就得做下去，就像一本书，读了开头，怎么好意思不读中间就直接看到结尾了。希望自己也希望各位都能在未来有所惑。</li>
  <li>Todayisthelastdayoftheyear,andasusual,it’stimeformyannualreview.Lastyear,afterreceivingmygraduateschoolexamresults,Idecidedtolookforwork.Iendedupatatraditionalcompanydoingfrontenddevelopment.MydailytasksmainlyinvolvedmodifyingJSPpages.Thoughthepaywasn’tmuch,itwasrelaxed.Duringmyfreetime,Istudiedmodernfrontenddevelopmentconcepts.I’mstilllearning,andthemoreIlearn,themoreIrealizehowmuchtechnicaldebtIhavetocatchupon.Atthebeginningoftheyear,IwaslearningReact.Duringthisperiod,Idiscoveredvariousopen-sourceprojects,UIcomponentlibraries,andlearnedtouseTailwindCSS,usingthesetechstackstocreatemanysmallcomponents.InApril,Istartedanopen-sourceprojectcalledIMakerusingReactasthefrontendframeworkandNEXTUIastheUIcomponentlibrary.Ithasnowgarneredover200starsonGitHub.ByMay,IMakerhadgainedover100stars,andIstartedsendingoutresumesandinterviewingatnewcompanies.Afewcompaniesleftdeepimpressions.Onewasalegalexaminationpreparationcompanywitha10/10/6workschedule.Thefirstinterviewertheretaughtmealot,helpingmeunderstandthatlearningtechnologyisn’tjustaboutframeworksbutaboutunderstandingwhat’sbehindthem.AnotherwasamilitaryindustrycompanythatIreallyliked.Iremembertheinterviewdaywasextremelyhot.Therewerejusttworounds-atechnicalinterviewthatwasn’ttoodifficult,andanHRinterviewthesameafternoon.Theyaskedsomequestionsandaddressedmyconcerns,thentoldmetowaitfortheirresponse.Thatwastheendofit.Later,whenItriedmessagingtheHRpersononWeChat,theydidn’trespond.AfterJune,astheweathergothotter,Ibecamemoreanxious.Istartedapplyingforpositionsinothercities,sendingresumesviaemailtocompanywebsitesandforumswhereseniorshadlefttheircontactinformation.Thereweresomeinterestingexperiencesduringthisperiod.Duringthistime,IattendedMicrosoft’sAIDayinBeijing,whereImetmanyexpertsandlearnedaboutcurrentindustrytrendsandfuturedirections.Mymaingoalwasstilltofindopportunities,andIsubmittedresumestosomecompanies,butwasn’tsuccessful.ThefeedbackwasmostlythatIlackedexperience-theywantedmoreseasonedprofessionals.IrememberapplyingtoacompanyheadquarteredinBeijingwithabranchinQingdaothatworkedonoperatingsystemsandhardware.AfterIsentmyemail,theHRresponseleftalastingimpression.Theysaid,“Asyoumentioned,yourresumeisn’timpressive,andyourworkexperienceandeducationalbackgrounddon’tmeetourcompany’saveragestandards…”Thishitmehardatthetime.Previously,Imighthaveacceptedthatmyeducationandworkexperiencewereordinaryanddidn’tmeettheirstandards.Butnow,Ithinkit’sokay-takeitslow,andtherightopportunitywillcome.However,atthatpoint,mymindsetwascompletelyshattered.Icouldn’tfocusonlearning,couldn’tsettledown,anddidn’tknowwhatIwasdoingorshouldbedoing.Iworriedaboutmylackofpracticalprojectexperience,non-standarddevelopmentpractices,andthemanythingsIdidn’tknow.Ikeptwonderingwhattodo,feelingstuckinalearningbottleneckwithnoclearpathforward.Mygirlfriendadvisedmetobepatient,stayatmycurrentcompany,andproperlylearnwhatIneededtolearn,butIdidn’tlisten.InJuly,Ihadasuddenideatotryoverseasopportunities,butIfoundtheoptionswerelimited,mainlybecauseIonlyhadayearofexperience,andmostpositionsrequiredthree,five,oreventenyears.Whilebrowsingonline,IfoundGitHubfounder’semailaddress.Takingachance,Iwrotehimanemailexpressingmyconfusionaboutmyfutureanddifficultyfindingnewopportunities.Surprisingly,hereplied,suggestingIgetinvolvedinopensourceandbecomeasolidcontributortosomeprojects.Hementionedthismightbeawaytobypasstraditionalinterviewsabroad.Thisgavemenewinspiration.GitHubistrulyatreasuretrove-Ilaterrealizedthatyoucanfindalmostanythingyouwanttolearnthere.Later,IreceivedamessagefromanotherseniordeveloperwhoseprojectIfoundonGitHub.AfterIemailedthem,theyadvisedmenottolimitmyselftotechnology,buttothinkfromaproductperspective-tocreateanddeployaproductfromscratch,asthisprocessteachesyoumanythings.Theyencouragedmenottogiveupandtokeeppersisting.I’mtrulygratefultoalltheseseniorsImetthroughtheinternet!Later,Ireceivedanofferfromadomesticmind-mappingcompanyinShenzhen.Aftersayinggoodbyetomyfriends,IboardedaflighttoShenzheninmid-August,thinkingthiswasanewbeginningheadinginagooddirection…Aftercompletingvariousonboardingprocedures,Istarteddevelopingfeaturesimmediately,withnotimetofamiliarizemyselfwiththecodebase.Sincemypreviousemployerwasn’tfocusedonmodernfrontenddevelopment,someofmyself-taughtknowledgewasn’tsufficient,whichmademenervous.Fortunately,Imanagedbyaskingseniorcolleaguesforhelpandstudyingafterreturningtomyrentalapartment.Later,duetomyknowledgegaps,Ihadsomecommunicationissueswiththeremoteteammembers(Iwastheonlyoneworkingon-site),whichcausedsomedevelopmentdelays.InSeptember,Shenzhen’sweatherwasincrediblyhumidandhot,whichwastrulyunbearable.Addtothattheflyingcockroaches,whichwereterrifying.CombinedwithdevelopmentissuesatthenewcompanyandthefactthatIdidn’tknowanyoneinShenzhen,Iwouldcryaloneinmyrentalapartmentatnight.InOctober,mygirlfriendcametovisitmeinShenzhen,whichmademeveryhappy.Ishowedheraroundvariousattractions,andatthatpoint,everythingseemedworthit.However,justwhenIthoughtthingswereontrack,thecompanyinformedmethatIhadn’tpassedmyprobationperiod.Theirreasonwasthatmyearlierdevelopmentissueshaddelayedotherteammembers’work,thoughIrecallonlyhavingsuchproblemsinthefirsttwoweeks,withonlyminorcodereviewissuesafterward.Buttheywouldn’tletmedefendmyself.MylastdaywassetforOctober30th,whichhappenedtobemybirthday.Interestingly,IreceivedabirthdayJDgiftcardthatday.Acolleaguewhosatnexttomeexpressedregretandchattedwithme,givingsomelearningadvicebeforeIleftthiscompanywhereI’dspentlessthanthreemonths.Afterseeingoffmygirlfriend,Iwasaloneinmyrentalapartment,fallingbackintoself-doubt.IdecidedtoleaveShenzhensinceIhadcometherespecificallyforthiscompany.BylateNovember,Ihadreturnedtomyhometown.OnethingthatreallybotheredmewasthattheShenzhencompanymademesignanon-competeagreementbeforeleaving.Ididn’tunderstandthiswell-usually,theseareforlong-termemployees,sowhyrequireitfromanewhire?Andtherewasnocompensation;theyjustshowedmethedoor.Backhome,Imetwitholdfriendswhohadallfoundtheirpathsinlife.ItfeltlikeIwasbackatsquareone.BythenitwasDecember,andbesidessendingoutresumes,Ispentmytimeself-studying.ThoughmytimeattheShenzhencompanywasbrief,itgavemeabasicframeworkforunderstandingproducts,andnowit’sjustamatteroffillingintheskillgaps.However,theyear-endhiringsituationdoesn’tlookpromising,withbarelyanyinterviews.I’munsureaboutmynextsteps.Tosummarizethisyear’sactivities:workingonopen-sourceprojects,joininganewcompany,andbecomingunemployed.Analyzingthisyear’seventsrevealsmybiggestproblem:impatience!Iactwithoutthinkingthingsthrough,withoutconsideringconsequences,andwhenthingsgetcritical,I’mlefthelplesswithnobackupplan.NowIrememberwhatmygirlfriendsaid,butasanadult,Ihavetobeartheconsequencesofmyactions.NowthatI’vecalmeddownandreflectedonthisyear’sevents,IrealizeIdidn’tthinksomethingsthroughproperly.Idon’tknowifthiscountsaspersonalgrowth,butthisexperiencehastaughtmealot,especiallytheimportanceofstayingcalm.Lookingbackatlastyear’ssummary,Iexecutedmostofmylearninggoalswell,butfailedto“staycalm.”Ican’tcontinuelikethisthisyear-it’stimetoseriouslyconsidermyfuturepath.Here’smyGitHubcontributiongraphfortheyear-I’llkeepworkinghardnextyear!!Finally,Iwisheveryonecandowhattheyloveinthenewyear,andmaymygirlfriendandparentsstayhealthy.</li>
  <li>今天是2024年的8月15日，距离六月份已经过去了两个多月了，本来标题想取‘年中总结’的，不过现在好像也不是年中了。所以，想来想去，想不到好的标题，恰好听到了斗破苍穹动漫的插曲，《少年泪》，音乐很好听，动漫也有很多触动我的地方。动漫中最喜欢的地方，大概就是主角一路走来，磕磕绊绊，且走且战，虽然九死一生，但还是坚持了下来。这里面有一个篇章是三年之约，是这部作品一个十分重要的章节，动漫制作组，将这段故事还原的也十分精彩，推荐大家去观看。说回到标题上面，‘少年泪’简单理解就是，少年在哭泣，我不知道自己还是否算是少年，我今年已经24岁了，好像应该算是青年了。不过，自己这一路走来实在是太不容易了，虽然和朋友聊起来的时候，自己总是嘻嘻哈哈的，但是实际怎么样，也只有自己知道。5月末的时候，写过一篇文章节流-防抖以及这段时间来的感受，虽然里面介绍一些关于节流和防抖的概念，但写那篇文章，主要还是发泄一些情绪。当然，并没有一些奇怪的言论，只是很普通的叙述一些事情。此时此刻，我已经离开了北京，来到了深圳，这里的温度空气和北京差别很大，不过我很喜欢这边的空气，比较湿润，氧气吸进肺部感到很舒服，这让我这个北方人很欣慰。是的，我找到一份新的工作，下周就要入职了，我很喜欢这家公司，我使用他们家的产品已经很多年了，没想到自己有机会成为自己喜欢产品的开发者，这对我来说，是一件十分幸运和有挑战的事情。接到offer的那一瞬间，我的情绪出奇的稳定和冷静，女朋友在旁边也很替我高兴，她甚至比我还要激动，她说，“才不是幸运，这就是你努力的结果。”是这样吗？我不知道，只是深深地吸了一口气，轻轻地吐了出去。回想这大半年来，也是有很多有意思的地方，第一次去从来没有听过，而且很偏很偏的地方参加面试；完成了第一个从零到一的开源项目，幸运的得到了一些关注，甚至还有投资者发来邮件，虽然最后并没有通过评审，不过还是很感谢；跟着女朋友学到了很多菜的做法，好像现在做素菜我已经强过她了，虽然她不承认，但是，whocare！当然还有很多，也有许多心酸的时候，求职期间屡屡碰壁，一度怀疑自己，躲在出租屋里偷偷地哭，不过好在坚持了下来，没有自暴自弃，我开始向全国，甚至向全世界投递简历，很幸运的是，有些国内和国外的公司回复了我，虽然邮件当中，他们说明了我的个人情况并不是很适合他们的团队，但是，这给了我启发，我知道了自己还有哪些不足的地方，需要补充和完善。其中，这些邮件当中，最没有想到的就是GitHub的创始人（TomPreston-Werner），回复了我的邮件，我十分的震惊，真的没有想到他竟然真的能够回复我，这简直不可思议，我在邮件中写道，现在求职环境似乎不是很好，想寻求一些关于求职的建议，他回复我说，可以参与一些开源项目，成为坚实的开源项目维护者。这对我也有了一些新的启发，原本我很多学习的内容就是从一些开源项目中学到的，所以，后面我在自己课余的时间，仍然会写自己的开源项目，每次打开一些开源项目的时候，看着前辈的代码风格，和严谨的逻辑，感叹，写的真好，就是不是自己写的。这一路走来，经历了很多，虽然说不上自己有多少成长，但是还算欣慰，至少自己并没有荒废时间，接下来的日子里，就该开启下一个故事了。虽然我不知道，未来会发生什么，但希望会向着好的方向发展。我逐渐意识到一点，就是我们会对未知的事情充满恐惧，或者对没有发生的事情，感到迷茫，我想说的是，让它顺其自然，如果发生了，那么就坦然接受，如果还没有发生，那就做好你能做好的准备。虽然现在大环境不好，我对未来仍然充满希望，我所说的希望，不是我未来能做成什么大事业，只是很简单的一点，我想要做自己的事情，我喜欢编程，虽然我的代码水平不是很高，但我还是很喜欢写下代码的那一刻，逻辑正确，项目跑通的那一瞬间的喜悦。所以，别怕，想哭的时候，就哭一哭吧，哭完了对自己也是一种释放，虽然很难，但生活还是得照常过，我现在仍然有很大的压力，仍然有很多事情需要我去一件一件的去完成，去解决。感谢这段时间以来遇到的所有人，希望你们在未来的日子里都能够有所收获。感谢我的女朋友女朋友！最后，希望我女朋友的身体变得更好一些。</li>
  <li>这几天复习到了两个概念，一个是节流，一个是防抖，他们都是可以用于浏览器性能优化。不过在说明节流和防抖的概念之前，先说一下call、apply、bind这三个函数call、apply、bind这三个函数都是JS中函数的方法，是用来改变函数调用时的this绑定并传递参数，换句话来说，就是改变this的指向。它允许你在不同的上下文中调用一个函数，这些方法都是Function原型，所以所有函数都可以直接调用它们。下面来分别看看它们仨callcall方法调用一个函数，并指定 this 的值和单个参数列表。func.call(thisArg,arg1,arg2,...)thisArg：调用函数时 this 的值。arg1,arg2,...：要传递给函数的参数列表。示例：functiongreet(intro,time){return`${intro}--${this.name},${this.age}--${time}`}constperson={name:'Tim',age:20,}console.log(greet.call(person,'你好','2024'))//你好--Tim,20--2024分析：注意看，return里面的内容用到了this，如果没有使用call改变this指向的话，那么this的指向应该是window。applyapply 方法与 call 方法类似，但它接受一个参数数组（或类数组对象）作为第二个参数。func.apply(thisArg,[argsArray])thisArg：调用函数时 this 的值。argsArray：一个数组或类数组对象，包含调用函数时要传递的参数。functiongreet(intro,time){return`${intro}--${this.name},${this.age}--${time}`}constperson={name:'Tim',age:20,}console.log(greet.apply(person,['你好','2024']))//你好--Tim,20--2024functionadd(a,b){returna+b}constnumbers=[2,3]console.log(add.apply(null,numbers))//输出:5可以看到apply和call的区别很小，只不过是参数变成了数组bindbind 方法创建一个新的函数，在调用时将 this 绑定到提供的值，并在新的函数中预设一定的参数。constboundFunc=func.bind(thisArg,arg1,arg2,...)thisArg：调用新函数时 this 的值。arg1,arg2,...：预设的参数。functiongreet(intro,time){return`${intro}--${this.name},${this.age}--${time}`}constperson={name:'Tim',age:20,}constbindFun=greet.bind(person,'你好啊🤔')console.log(bindFun('2024~~'))//你好啊🤔--Tim,20--2024~~总结call 和 apply：都用于立即调用函数，并改变 this 的值。区别在于 call 接受参数列表，而 apply 接受参数数组。bind：用于创建一个新函数，该函数在调用时将 this 绑定到提供的值，并可以预设部分参数。新函数不会立即执行，而是可以在稍后调用。使用场景call：适用于函数立即调用且参数数量固定的情况。apply：适用于函数立即调用且参数以数组形式传递的情况，特别是参数数量不固定时。bind：适用于创建一个新的函数，并在稍后某个时刻调用该函数，同时预设 this 绑定和部分参数。接下来我们来自己实现一个call方法Function.prototype.myCall=function(context){//检查调用者是否是函数if(typeofthis!=='function'){thrownewTypeError('Error')}//如果没有提供context，默认使用全局对象（浏览器中是window，这里是非严格模式，严格模式是undefined）context=context||windows//将当前函数（即this）作为context对象的一个属性fn，这样，我们就可以通过context.fn调用这个函数，并确保this指向context对象context.fn=this//获取传递的参数//arguments是一个类数组对象，包含传递给myCall的所有参数。使用扩展运算符...将其转换为真正的数组，并使用slice(1)方法去掉第一个参数（即context），获取剩余的参数。constargs=[...args].slice(1)//调用函数，并传递参数constresult=context.fn(...args)//删除临时添加的属性deletecontext.fn//返回函数调用的结果returnresult}ok，现在你也可以发现，既然它们可以改变this的指向，那么就可以用它们来实现节流和防抖了，先来介绍一下节流防抖的概念节流和防抖节流（Throttle）和防抖（Debounce）是两种用于优化高频率函数调用的技术。它们都可以帮助减少函数的执行次数，从而提升性能，但它们的工作方式和适用场景有所不同。节流节流的作用是在规定的时间间隔内，保证一个函数最多执行一次。节流常用于处理那些频繁触发的事件，比如滚动、窗口调整大小、鼠标移动等。我们来看一下具体实现，先来不需要apply的版本//function需要被节流的目标函数//delay约定的时间，以毫秒为单位。在这个时间间隔内，目标函数最多只能执行一次functionthrottle(func,delay){//记录上一次目标函数被调用的时间戳，初始值为0。letlastCall=0//返回一个新的匿名函数，这个函数包裹了目标函数func并添加了节流逻辑。//...args：使用ES6的扩展运算符（spreadoperator）来获取传给这个匿名函数的参数，并将这些参数传递给func。returnfunction(...args){//获取当前时间constnow=newDate().getTime()//检查时间间隔，如果当前时间减去上次触发时间小于约定的时间段，直接返回；也就是说，用户在这个时间段内触发了多次//但是由于还在这个时间段内，所以我们只执行一次if(now-lastCall&lt;delay){return}lastCall=now//将参数传递给目标函数，并调用它returnfunc(...args)}}下面是一个完整的使用用例，当用户拖动窗口的时候，在约定的200毫秒内，控制台只打印一次functiononResize(){console.log('Windowresized',newDate().getTime())}functionthrottle(func,delay){letlastCall=0returnfunction(){constnow=newDate().getTime()if(now-lastCall&lt;delay){return}lastCall=nowreturnfunc()}}constthrottledResize=throttle(onResize,200)window.addEventListener('resize',throttledResize)接下来我们用apply函数实现一下节流functionthrottle(func,delay){letlastCall=0returnfunction(...args){constnow=newDate().getTime()if(now-lastCall&lt;delay){return}lastCall=nowreturnfunc.apply(this,args)}}这两种实现方式，核心逻辑上面完全一样，区别只是在参数传递上使用apply方法更适合已经有参数数组的情况，并且需要显式指定this上下文。使用扩展运算符…args更简洁，适合直接处理参数列表且不需要显式指定this上下文的情况。防抖防抖（Debounce）是一种用于优化高频率函数调用的技术，它确保一个函数在事件结束后的指定时间内只执行一次。如果在这段时间内再次触发事件，则重新计时。防抖技术常用于处理那些需要在用户停止操作后执行的事件，比如输入框提示、表单验证等。原理：防抖通过设置一个延迟时间（如200毫秒），在事件触发后开始计时。如果在延迟时间内再次触发事件，则清除之前的计时器并重新开始计时。只有当延迟时间过去后没有再次触发事件，函数才会执行。functiondebounce(func,wait){lettimeoutreturnfunction(...args){clearTimeout(timeout)//设置一个新的计时器，在延迟时间wait之后调用目标函数func。//使用箭头函数（=&gt;）确保this的值在回调中与外层函数一致。//func.apply(this,args)：使用apply方法调用目标函数func，并确保this上下文和参数args被正确传递。timeout=setTimeout(()=&gt;{func.apply(this,args)},wait)}}示例：functiononInput(){console.log('Inputevent',newDate().toISOString())}constdebouncedInput=debounce(onInput,300)constinputElement=document.querySelector('input')inputElement.addEventListener('input',debouncedInput)onInput函数只有在用户停止输入300毫秒后才会执行。如果用户在300毫秒内继续输入，计时器将重新开始。好了，来聊聊最近的感受吧说实话，这段时间过得并不好，该从哪里说起呢，就从这篇文章来说起吧。其实今年我一直想写文章，不管是技术学习上的还是生活上的，但是我今年一直在找机会，找一个新的工作；所以我心想，等我找到一个工作之后，就开始写文章，因为我可以学习公司的业务，没准可以产出更好的文章。但是我的希望落空了，年后我就一直在陆陆续续的投递简历，但是没什么回应，后来我开始学习react，看了很多教程，也做了一些笔记，都存放在我的obsidian上了，上个月22号，我开始写一个react项目IMaker，我花了一周左右的时间去写，到现在我也在修改commit，到今天这个项目在GitHub上大概有200star了。这是我第一次获得这么多的star，心中有些窃喜，我大概花了两周左右就到了100，于是我赶紧修改简历，加上这个新的项目，开始投递简历，第一周有两个面试，一家是web3，一家是小红书的外包，我感觉自己答的还不错，基本都回答上了，可是就是没有下文。第二周，没有任何回应，我又开始有些焦虑了，第三周没有回应，我不知道是哪里出了问题，也许确实是我的业务能力和技术不太行。我开始怀疑自己，甚至有些抱怨，但后来我意识到，这样下去没有任何的用处，除了消耗自己，还是消耗自己。前两天做了一个线上测试，有几道算法题没有写出来，不出意料，挂了，但是我并没有太过失落，至少我知道了自己的一些短板，我想在后面的时间里去练习算题题，虽然我真的很不喜欢算法题。这几天我听到一些话语，大概意思就是，“你觉得自己不幸，可你没有跟那些真正不幸的人对比过”。我想了想，确实是这样，我只不过是暂时找不到一份合适的工作，四肢健全，身体也没有什么太大的问题，已经十分幸运、十分幸运、十分幸运了。我来到这个世上不是为了内耗自己，即使生活过得不怎样，可我保持对生活的乐观，不矢为一件幸事。我人目前在北京，只有一年多的经验，想找一份前端的工作，技术栈是vue3和react这是我的简历我的邮箱是：********************如果您有工作机会，欢迎联系我如果您有任何想问的，或者想跟聊聊，欢迎邮件联系我。</li>
  <li>WelcomeThisisasampleblogposttodemonstratethemultilingualsupportfeature.ThisarticleisavailableinbothEnglishandChinese,andyoucanswitchbetweenlanguagesusingthelanguagetogglelinkatthetop.KeyFeaturesCompletemultilingualsupportIndependentcontentmanagementEasylanguageswitchingCodeExampleHere’sasimplePythoncodeexample:defgreet(name):print(f"Hello,{name}!")greet("World")TableExampleFeatureDescriptionLanguageSwitchSupportforChineseandEnglishArticleTranslationIndependentarticletranslationsystemInterfaceLocalizationCompleteinterfacetranslationImageExampleQuoteExampleSupportingmultiplelanguagesinablogiscrucialasitallowsyourcontenttoreachawideraudience.ConclusionThisisasimpleexamplepostdemonstratingthemultilingualsupportfeaturesoftheblogsystem.Youcanusethistemplatetocreateyourownmultilingualblogposts.</li>
  <li>今天是2023年的最后一天，过了今晚十二点，这一年就结束了。去年我也是差不多年末的时候写了一篇年终总结，今年秉承去年的习惯继续来写今年的故事。该从哪里说起呢？那么就从二月份开始吧，因为我记得大概是二月中旬的时候知道了考研的成绩，知道分数的那一刻，我就知道去年的努力算是付诸东流了。知道结果的时候，其实并没有太过失落，甚至是放松了一口气，因为接下来我就可以全心去找工作了。大学的时候我学习的Java相关的内容，由于考研的缘故，很多内容基本都忘记了，因此我想学习前端相关的知识，当时想的这个入门快，看文档加上练习很快就能堆出一个网页来。现在想想，还是我太年轻了，低估了前端方向里面的内容，也高估了自己的学习能力。开始学习前端之后，我在家附近办了一个月的健身卡，当时就是上午学习，下午健身，晚上回来接着再学一会，我还挺佩服自己的，除了下雨那几天，基本每天都去健身房，虽然效果一般，哈哈哈，不过后来也是可以一口气做上二三十个俯卧撑了。我快速过完的HTML、CSS和JS的基础内容之后，然后就开始框架的学习，我一开始学习的是Vue2，紧接着我就发现事情越来越不对劲了，因为要学的东西太多了，根本学不完，光是前端框架就有Vue、React还有Angular，Vue框架还有Vue3版本，我的天，怎么会有这么多，紧接着，用来发送请求的Axios框架，还有组件框架Element(Vue2)和Element-plus(Vue3)，然后还有还有JS的‘哥哥’Typescript！！那几天真的很焦虑很焦虑，晚上的时候会睡不着，也不敢刷小B站，因为推送的全是前端相关的，白天吃饭的时候，老妈经常会问什么时候投简历啊，去哪里啊？我总是说还没有准备好，还有很多要学的东西，不过也只有自己知道这只是我逃避就业的一个理由罢了。一边学一边哭，哎，后来的操作就是掌握上面最简单最简单的使用，深入了解是没有时间的，然后跟着视频教程做了一个Admin的小项目，把所学的内容串了起来。然后我就开始修改简历，准备开始找工作。我以为这次应该会好很多了吧？至少！结果我又傻眼了！！失联招聘！！Boss直拒！！前程堪忧！！58不成！！投出的简历基本全是已读不回，我去网上翻了一些帖子才发现今年真的真的真的太难了！！而且裁员现象也很严重，这算是两年的脱产考研带来的不好的后果之一吧。随后我转变思路，在网上请教了一些前辈，改了改简历，算是比原来‘好看’一些了，然后分别每天下午投简历，想着这时候看到的几率比较大一些。好在我运气比较好，拿到了几个面试，第一家是北京的一个xx银行信用卡中心，去了之后才傻眼了，简直了，跟他们在Boss上写的招聘需求完全不一样。去了先去填个表格，个人信息之类的，然后就开始安排面试，有点让我大开眼界，一点技术问题没问，问你家里哪的，就像是在查户口。大约二十分之后面试结束，我在外面搜了一下这家公司，这就是一个办信用卡的，网上很多人被骗了，就是拿着假的JD招聘开卡的人。这算是被社会毒打的第一步？？？紧接着，又过了一周，拿到一个石家庄的面试，这个面试很简单，也过了，但是他让我试岗，完事才能给我签合同，当时我还不太懂试岗是什么意思，后来在论坛搜了一下，我真的是，哎，没办法，谁让现在大环境不好，人太多了呢！再后来就是现在这家公司了，怎么说呢，一开始确实是被HR的介绍完全唬住了，说我们公司做海外结算，作为第一家公司是个很好的选择，然后我就稀里糊涂的入职了。入职之前，还是先回家，参加了一个发小的婚礼，突然有点羡慕人家，同样的年纪，他已经安家结婚了，我却刚刚开始在外漂泊，不过还是和朋友们一起玩耍了几天，婚礼进行的很顺利，大家都很开心。只不过我早早离场，因为我要回家收拾行李，准备去北京了。来北京之后，先是去了酒店，也是为了省点钱，选了一家便宜点的，然后他没有窗户，隔音还很差，半夜还有人唱歌，我真的，哎。第二天联系了网上提前约好的中介，挨个去看房，有一些房子很好，但是价格很贵，后来我回到酒店，来到前台买了桶泡面吃，就跟老板聊起来了，刚好老板有个认识的中介，推给我了，完事就带着我去看房，这个房老而且环境很差，可是房租要低很多，考虑再三我答应了。第三天签租房合同。其实关于租房的这段经历还有很多，但我不太想多说什么，经历过北漂的人就知道中介有多么恶心，一个二居室能给你分出五个屋来，甚至还有的拖欠押金不还，相信有太多、太多朋友被坑过了。入职工作之后，我并没有如一开始想象的那样，我在家学到的东西在工作上基本用不到，2023年了，竟然还有JSP页面，他们的项目目录也十分的混乱，都是存放在服务器上面，找个文件竟然要在盘符当中一个一个去搜，去找。这家公司虽然有很多不好的地方，但至少他给我开了一份我能够生存下去的薪水，虽然不多。在这家公司呆的这几个月里，我自学完了很多内容，重新学习了Vue3，还有学习了一部分Ts，还有用来发送请求的Axios，还学了Node，还有它的框架，Express和NestJs，学的东西其实都是围绕前端方面，这里面的NestJs虽然是一个后端框架，但远远不到熟练使用的地方，也仅仅是写简单的CRUD。2024年的学习计划：学习React学习NextJs如果时间足够的话，我想重新再学习下Java的一些知识接下来的这一年，我希望自己能够做到的还是沉下来，耐心的学习，一点一点的去扣一些内容，不要着急，焦虑总是会有的。还有一些内容值得思考，那就是现在的就业大环境，前端这个方向人确实太多太多了，Java现在已经卷成麻花了，Go会不会是一个好的方向？？对于未来怎么样，真的不得而知，所以，我接受自己的平庸，自己就是一个普通人。我还想谈一谈我的女朋友，和她的认识很意外，五一假期出去玩，她坐我旁边，然后就这么神奇的认识了，其实当时没想着会发生什么，但好像真的就像是有种魔力，牵引着两个人。跟她相处到现在已经有半年了，也有一些小摩擦，但是总体来说还是没有什么问题的，她这个人很善良，也很有耐心，特别喜欢看她认真学习的样子，她学习的时候特别投入，就跟个小猫似的，累了就在那伸伸懒腰，感觉好可爱。希望来年我们两个也能继续好好的！这一年其实过的挺平淡的，笑过也哭过。在最后的几天里，见到了一些大学时候关系就很好的朋友；有一个是快两年没见了，一起吃了饭，聊了很多，他今年背上了房债，已经开始相亲了，对此我也只是感叹时间真的过得好快，好像昨天我们还坐一块吹牛皮捣蛋，时间真的过得好快好快。另一个朋友在北京上学，我们两个也是厉害，一个下午走过了四个地铁站，晚上的时候已经快三万步了，两个人就没有方向的瞎走，也不看地图就是瞎走，没想到走到了中关村，看到了微软总部，它的设计风格很喜欢，要是有一天我也能去微软就好了。中午一起吃了驴肉火烧，哈哈哈，就在人大附近的一个小店里，味道很不错，他家的重庆小面也很好吃，酸辣口味的。还有很多事没有提到，这一年真的好快，自己的变化也很多，变得不再那么容易焦躁，开始考虑一些事情的后果，又或是变得有些胆怯，不再像以前想到什么就去做什么，这算是一种成长吗！应该算吧，还有很多不懂的东西，还有很多不了解的事情，需要学习，需要去了解。这一年有的时候自己会想，好难受，不过还是劝自己再坚持一下，不是还有很多事没有做吗？最后，也算是给自己一些慰藉吧，把心态放平，随心就好，学不会的东西就多学几次，加油！新年快乐，祝自己，祝她，也祝大家。</li>
  <li>闭包closure概念：一个函数对周围状态的引用捆绑在一起，内层函数中访问到其外层函数的作用域简单理解：闭包=内层函数+外层函数的变量functionouter(){consta=1functionf(){//内层函数用到了外面的变量aconsole.log(a)}f()}outer()//常见的闭包格式，外部可以访问使用函数内部的变量functionouter(){leta=100functionfn(){console.log(a)}returnfn}闭包应用：实现数据的私有例如，做一个函数统计，调用次数，函数每调用一次，++letcount=1functionfun(){count++console.log(`函数被调用了${count}次`)}//存在一个问题，就是i全局变量，很容易被修改functionfn(){letcount=1functionfun(){count++console.log(`函数被调用了${count}次`)}returnfun}constres=fn()//实现了数据的私有，这样就可以有效解决问题，注意i并没有被js的垃圾回收机制回收，所以就存在一个内存泄漏问题变量和函数提升变量提升是JavaScript中，允许变量声明之前被访问（仅存在于var变量）注意：变量在未声明即被访问时会报语法错误变量在var声明之前被访问，不会报错，变量值为undefinedlet/const声明的变量不存在变量提升变量提升出现在相同作用域中实际开发中推荐先声明再访问变量functionfn(){console.log(num)//undefinedvarnum=10}fn()//1.把所有var声明的变量提升到当前作用域的最前面//2.只提升声明，不提升赋值函数提升函数在声明之前即可被调用，函数提升只出现在相同作用域上fun()//声明函数functionfun(){console.log('声明之前即被调用')}fun()//错误varfun=function(){console.log('函数表达式不存在提升现象')}函数参数动态参数arguments是函数内部内置的为数组变量，它包含了调用函数时传入的所有实参function(){letsum=0;for(leti=0;i&lt;arguments.length;i++){sum+=arguments[i]}console.log(sum)}sum(1,2,3)//6sum(1,2,3,4,5)//15//arguments是一个*伪数组*，只存在于函数中//arguments的作用是动态获取函数的实参剩余参数…是语法符号，用于获取多余的实参是个真数组functionfun(a,b,...other){console.log(a,b)//12console.log(other)//[3,4,5,6]是个数组}fun(1,2,3,4,5,6)展开运算符注意与剩余参数区分，语法符号相同只是用法不同constarr1=[1,2,3,4]console.log(...arr1)//1234//1.求数组最大值console.log(Math.max(...arr1))//4//2.合并数组constarr2=[7,8,9]constarr=[...arr1,...arr2]console.log(arr)//[1,2,3,4,7,8,9]//展开运算符主要是数组展开//剩余参数在函数内部使用箭头函数this箭头函数不会创建自己的this，它只会从自己的作用域链上一层沿用thisconsole.log(this)//Window//普通函数的thisfunctionfun(){console.log(this)//Window}fun()//对象里面函数的thisconstobj={name:'xiaoming',age:20,fun:function(){console.log(this)//{name:'xiaoming',age:20,fun:ƒ}}}obj.fun()//箭头函数的thisconstfun=()=&gt;{console.log(this)//Window}fun()//对象函数中箭头函数的thisconstobj={name:'xiaoming',age:20,fun:()=&gt;{console.log(this)//Window}}obj.fun()对象解构const{uname,age}={uname:'xiaoming',age:18}//等价于const=uname=obj.uname//要求属性名和变量名一致console.log(uname)//xiaomingconsole.log(age)//18//给新的变量名赋值const{name:uname,age}={name:'xiaoming',age:18}console.log(uname)//多级对象解构constper={name:'小明',family:{mother:'小红'}age:20},const{name,family:{mother}}=perconsole.log(name+'---'+mother)forEach遍历数组forEach就是遍历，加强版的for循环，它没有返回值constarr=['red','green','pink']constresult=arr.forEach(function(item,index){console.log(item)//redgreenpinkconsole.log(index)//012})console.log(result)//undefined数组reduce累计方法//无初始值constarr=[1,3,5]consttotal=arr.reduce(function(pre,cur){returnpre+cur})console.log(total)//9//有初始值constarr=[1,3,5]consttotal=arr.reduce(function(pre,cur){returnpre+cur},10)console.log(total)//19//箭头函数写法constarr=[1,3,5]consttotal=arr.reduce((pre,cur)=&gt;pre+cur,10)console.log(total)//19原型主要是利用原型对象实现方法共享构造函数通过原型分配的函数是所有对象所共享的JavaScript规定，每一个构造函数都有一个prototype属性，指向另一个对象，所以也称为原型对象这个对象可以挂在函数，对象实例化不会多次创建原型上的函数，节约内存可以把一些不变的方法，直接定义在prototype对象上，这样所有对象的实例就可以共享这些方法构造函数和原型对象中this都指向实例化的对象functionPerson(name,age){this.name=namethis.age=age}Person.prototype.sing=function(){console.log('唱歌')}constp1=newPerson('小明',22)constp2=newPerson('小红',23)console.log(p1)console.log(p2)//Person {name:'小明',age:22}//age:22//name:"小明"//[[Prototype]]:Object//**sing:ƒ ()**//constructor:ƒ Person(name,age)//[[Prototype]]:Object//Person {name:'小红',age:22}//age:22//name:"小红"//[[Prototype]]:Object//**sing:ƒ ()**//constructor:ƒ Person(name,age)//[[Prototype]]:Object小结：1.原型是一个对象，称prototype为原型对象2.原型可以用来方法，可以把那些不变的方法，直接定义在prototype对象上3.构造函数和原型对象里面的this指向实例化的对象letthatfunctionPerson(name){this.name=namethat=this}constp=newPerson()console.log(that===p)//trueconstructor属性functionPerson(name){this.name=name}constp=newPerson('小明')console.log(Person.prototype.constructor===Person)//trueconsole.log(pinstanceofPerson)//trueconsole.log(p.constructor===Person)//true//p.constructor指向Person本身//原型对象中的constructr属性指向构造函数本身使用场景：如果有多个对象的方法，我们可以给原型对象采取对象形式赋值但是，这样就会覆盖构造函数原型对象原来的内容，这样修改后的原型对象constructor就不再指向当前构造函数了此时，我们可以在修改后的原型对象中，添加一个constructor指向原来的构造函数functionPerson(name){this.name=name}Person.prototype={sing:function(){console.log('唱歌')},dance:function(){console.log('跳舞')}}console.log(Person.prototype.constuctor)//指向ObjectfunctionPerson(name){this.name=name}Person.prototype={constructor:Person,//手动利用constructor指向Person函数本身sing:function(){console.log('唱歌')},dance:function(){console.log('跳舞')}}console.log(Person.prototype.constuctor)//指向Person原型链–查找规则当访问一个对象的属性（方法）时，首先查找这个对象自身有没有该属性如果没有就查找它的原型（也就是__proto__指向的prototype原型对象）如果话没有就查找原型对象的原型（Object的原型对象）以此类推一直找到Object为止深浅拷贝constperson={name:'小明',age:18}constp=personconsole.log(p)//{name:'小明',age:18}p.age=20//问题出现了，我只想改变p中的age，但是person中的age也跟着改变了console.log(person)//{name:'小明',age:20}浅拷贝和深拷贝只针对引用类型浅拷贝：拷贝的是地址constperson={name:'小明',age:18}//浅拷贝-1constp={...person}console.log(p)//{name:'小明',age:18}p.age=20console.log(person)//{name:'小明',age:20}console.log(person)//{name:'小明',age:18}//浅拷贝-2constp2={}Object.assign(p2,person)console.log(p2)//{name:'小明',age:18}p2.age=20console.log(p2)//{name:'小明',age:20}console.log(person)//{name:'小明',age:18}constobj={name:'tom',age:19,family:{wife:'jerry'}}consto={}Object.assign(o,obj)o.age=20o.family.wife='jerk'console.log(o)//age:20family:{wife: 'jerk'}name:"tom"//问题出现了，obj中的family也跟着改变了console.log(obj)//age:20family:{wife: 'jerk'}name:"tom"总结：1.直接赋值和浅拷贝有什么区别？*直接赋值的方法，只要是对象，都会互相影响，因为是直接拷贝对象栈里面的地址*浅拷贝如果是一层对象，不会影响，如果出现多层，则会互相影响2.浅拷贝怎么理解*拷贝对象之后，里面的属性值是简单数据类型直接拷贝的值*如果属性值是引用数据类型，则拷贝的是地址深拷贝：拷贝的是对象，不是地址常见实现深拷贝：通过递归实现深拷贝lodash/cloneDeep通过JSON.stringify()实现函数递归：如果一个函数在内部可以调用自身，这个函数就是递归函数但是，递归很容易发生“栈溢出”错误，所以必须要加退出条件returnfunctiongetTime(){document.querySelector('div').innerHTML=newDate().toLocaleString()setTimeout(getTime,1000)}getTime()callapplybind相同点：都可以改变函数内部的this指向区别点：call和apply会调用函数，并且改变函数内部this指向call和apply传递的参数不一样，apply必须传递数组形式[arg]bind不会调用函数，可以改变函数内部的this指向主要应用场景：call调用函数并且可以传递参数apply经常跟数组有关系，比如借助于数学对象实现数组最大最小值bind不调用函数，但是还想改变this指向，比如改变定时器内部的this指向性能优化防抖（debounce）单位时间内，频繁触发事件，只执行最后一次使用场景：搜索框搜索输入，用户最后一次输入完，再发送请求手机号、邮箱验证输入检测当你在JavaScript中需要实现防抖（debounce）功能时，你可以使用以下代码作为一个示例：functiondebounce(func,delay){lettimerId;returnfunction(...args){clearTimeout(timerId);timerId=setTimeout(()=&gt;{func.apply(this,args);},delay);};}//示例函数functionsearch(){//模拟搜索操作console.log('Searching...');}//创建防抖函数constdebounceSearch=debounce(search,500);//在输入框中绑定事件监听器constinput=document.querySelector('input');input.addEventListener('input',debounceSearch);在上面的示例中，debounce函数接收两个参数：func是要执行的函数，delay是延迟的时间间隔。它返回一个新的函数，该函数会在指定的延迟时间内被调用。在示例中，我们定义了一个search函数，它模拟了搜索操作。然后，我们使用debounce函数创建了一个名为debounceSearch的防抖函数，将search函数作为参数传递给它，并设置了延迟时间为500毫秒。最后，我们使用addEventListener将debounceSearch函数绑定到输入框的input事件上。这样，当用户在输入框中输入时，防抖函数将确保在用户停止输入一段时间后才执行搜索操作，从而减少了频繁触发搜索的次数。``节流（throttle）单位时间内，频繁触发事件，只执行一次以下是一个基本的JavaScript节流函数的示例：functionthrottle(func,delay){lettimerId;letlastExecutedTime=0;returnfunction(...args){constcurrentTime=Date.now();if(currentTime-lastExecutedTime&lt;delay){clearTimeout(timerId);timerId=setTimeout(()=&gt;{lastExecutedTime=currentTime;func.apply(this,args);},delay);}else{lastExecutedTime=currentTime;func.apply(this,args);}};}这个throttle函数接受两个参数：func是要节流的函数，delay是延迟的时间间隔（以毫秒为单位）。它返回一个新的函数，该函数在指定的时间间隔内最多执行一次。使用示例：functionhandleScroll(){console.log('Scrollevent');}constthrottledScroll=throttle(handleScroll,200);window.addEventListener('scroll',throttledScroll);在上面的示例中，我们定义了一个handleScroll函数来处理滚动事件。然后，我们使用throttle函数创建了一个节流的版本throttledScroll，并将其绑定到scroll事件上。这样，当用户滚动页面时，handleScroll函数最多每200毫秒执行一次，以减少事件的触发频率。</li>
  <li>ECMAScript相关介绍什么是ECMAECMA（EuropeanComputerManufacturersAssociation）中文名称为欧洲计算机制造商协会，这个组织的目标是评估、开发和认可电信和计算机标准，1994年后该组织改名为Ecma国际什么是ECMAScriptECMAScript是E尺码国际通过ECMA-262标准化的脚本程序设计语言。ECMAScript定义了一套脚本语言的标准，规定了语法、类型、语句、关键字、保留字、操作符、对象等方面的规范。JavaScript是ECMAScript的一种实现，同时还包括了浏览器和Node.js等环境提供的API和对象。由于ECMAScript是一种标准化的语言，所以不同的JavaScript实现都应该遵循ECMAScript标准，以确保代码的可移植性和互操作性。ECMAScript6新特性1.let关键字let关键字用来声明变量，使用let声明的变量有以下特点：不允许重复声明块级作用域不存在变量提升不影响作用域链应用场景：以后声明变量用let就行2.cosnt关键字const关键字用来声明常量，从const声明有以下特点：声明必须赋初始值标识符一般为大写不允许重复声明值不允许修改块级作用域注意：对象属性修改和元素变化不会发出const错误应用场景：声明对象类型使用const，非对象类型声明选择letconstarr=[1,2,3,4];arr.push(5,6);console.log(arr);//不报错constobj={uanme:'rick',age:20;}obj.age=11;//只要不改变地址，就不报错3.变量的解构赋值ES6允许按照一定模式，从数组和对象中提取值，对变量进行赋值，这被称为结构赋值//以下是一个使用数组解构赋值案例：constarr=[1,2,3];const[a,b,c]=arr;console.log(a);//1console.log(b);//2console.log(c);//3//以下是一个使用对象解构赋值案例：constobj={x:1,y:2,z:3};const{x,y,z}=obj;console.log(x);//1console.log(y);//2console.log(z);//34.模板字符串模板字符串是增强版的字符串，用反引号来标识，特点：字符串可以出现换行符可以使用${xxx}形式输出变量应用场景：当遇到字符串与变量拼接的情况使用模板字符串letname='jack';console.log(`hello,${name}`);letul=`&lt;ul&gt;&lt;li&gt;1&lt;/li&gt;&lt;li&gt;2&lt;/li&gt;&lt;li&gt;3&lt;/li&gt;&lt;/ul&gt;`5.简化对象写法在ES6中，我们可以使用简化对象写法来定义对象。这种写法可以让我们更加简洁地定义对象，避免了重复书写属性名和属性值应用场景：以后就用简写就行constname='John';constage=30;constperson={name,age};console.log(person);//{name:'John',age:30}//在上面的代码中，我们定义了一个 `person` 对象，使用了简化对象写法。我们只需要写出属性名，然后将变量名赋值给属性名即可。这样就可以定义一个具有 `name` 和 `age` 属性的对象。6.箭头函数ES6允许使用=&gt;定义函数function写法functionfn(param1,param2...){//函数体returnexpression;}=&gt;写法letfn=(param1,param2...)=&gt;{//函数体returnexpression;}注意：如果形参只有一个，小括号可以省略如果函数体只有一条语句，花括号可以省略，函数的返回值为该条语句的执行结果箭头函数this是静态的，时钟指向声明时所在作用域下this的值```js//用箭头函数定义constperson={name:‘Alice’,sayName:()=&gt;{console.log(this.name);}};person.sayName();//在这个例子中，箭头函数sayName被定义在person对象内部，因此它的this指向是person对象所在的作用域下的this值，即全局的this值。因此，当我们调用person.sayName()时，它会输出undefined。//用普通函数定义constperson={name:‘Alice’,sayName:function(){console.log(this.name);}};person.sayName();//在这个例子中，sayName方法使用普通函数来定义，因此它的this指向是动态的，即指向调用该方法的对象，也就是person对象。因此，当我们调用person.sayName()时，它会输出Alice。***箭头函数不能作为构造函数实例化**```js//箭头函数，不能被用作构造函数来创建实例化对象。这是因为箭头函数没有自己的this值，它的this值是继承自它所在的上下文constPerson=(name)=&gt;{this.name=name;};constjohn=newPerson('John');//TypeError:PersonisnotaconstructorfunctionPerson(name){this.name=name;}//普通函数constjohn=newPerson('John');console.log(john.name);//"John"箭头函数不能使用 arguments 对象arguments 对象是一个类数组对象，它包含了函数调用时传入的所有参数。在传统函数中，我们可以通过 arguments 对象来获取这些参数。但是，在箭头函数中，arguments 对象并不存在，因为箭头函数没有自己的 this 和 arguments 对象，它们会继承父级作用域的 this 和 arguments 对象functiontraditionalFunction(){console.log(arguments);}constarrowFunction=()=&gt;{console.log(arguments);}traditionalFunction(1,2,3);//输出[1,2,3]arrowFunction(1,2,3);//报错：argumentsisnotdefined//在这个例子中，我们定义了一个传统函数 `traditionalFunction` 和一个箭头函数 `arrowFunction`，并且在它们内部分别打印了 `arguments` 对象。当我们调用 `traditionalFunction` 时，它会输出 `[1,2,3]`，因为 `arguments` 对象存在并包含了传入的参数。但是，当我们调用 `arrowFunction` 时，它会抛出一个错误，因为 `arguments` 对象不存在//因此，如果我们需要在箭头函数中获取参数，我们可以使用剩余参数语法 `...args` 来代替 `arguments` 对象。constarrowFunction=(...args)=&gt;{console.log(args);}arrowFunction(1,2,3);//输出[1,2,3]7.剩余参数restJavaScript的剩余参数语法（restparametersyntax）用三个点（…）表示，用于将函数的多个参数收集成一个数组例如，以下函数的参数列表中使用了剩余参数语法：functionsum(...numbers){returnnumbers.reduce((total,num)=&gt;total+num,0);}console.log(sum(1,2,3));//6console.log(sum(4,5,6,7));//22在这个例子中，sum函数的参数列表中使用了剩余参数语法，将传入函数的所有参数收集成一个名为numbers的数组。函数内部使用reduce方法对数组中的所有元素求和，并返回求和结果需要注意的是，剩余参数语法只能用于函数的最后一个参数当我们在定义一个函数时，可以使用剩余参数语法来表示函数可以接受任意数量的参数。但是需要注意的是，剩余参数语法只能用于函数的最后一个参数举个例子，假设我们要定义一个函数，用于计算任意数量的数字的平均值。我们可以使用剩余参数语法来实现：functionaverage(...numbers){letsum=0;for(letnumberofnumbers){sum+=number;}returnsum/numbers.length;}在这个例子中，...numbers表示可以接受任意数量的参数，并将它们存储在一个数组中。我们可以像这样调用这个函数：console.log(average(1,2,3));//输出2console.log(average(4,5,6,7));//输出5.5需要注意的是，剩余参数语法只能用于函数的最后一个参数。例如，下面这个函数定义是错误的：functionexample(...numbers,x){//错误的函数定义，剩余参数语法不能用于函数的最后一个参数之前}8.函数参数默认值设定ES6允许给函数参数设置默认值，当调用函数时不给实参，则使用参数默认值，有默认值的形参一般要靠后letadd=(x,y,z=3)=&gt;x+y+z;console.log(add(1,2));//69.Spread扩展运算符JS中的扩展运算符（spreadoperator）是三个点（…），它可以将一个数组或对象“展开”成多个独立的值。下面是一些扩展运算符的使用示例：将数组展开成函数参数：constarr=[1,2,3];console.log(...arr);//123functionsum(a,b,c){returna+b+c;}console.log(sum(...arr));//6合并数组：constarr1=[1,2,3];constarr2=[4,5,6];constarr3=[...arr1,...arr2];console.log(arr3);//[1,2,3,4,5,6]复制数组：constarr1=[1,2,3];constarr2=[...arr1];console.log(arr2);//[1,2,3]将对象展开成另一个对象：constobj1={a:1,b:2};constobj2={...obj1,c:3};console.log(obj2);//{a:1,b:2,c:3}复制对象：constobj1={a:1,b:2};constobj2={...obj1};console.log(obj2);//{a:1,b:2}10.SymbolJavaScript中的Symbol是一种基本数据类型，用于创建唯一且不可变的值，通常用作对象属性的键名。Symbol值可以通过Symbol()函数进行创建，如下所示：constmySymbol=Symbol();Symbol值可以作为对象的属性键名，如下所示：constmySymbol=Symbol();constmyObj={};myObj[mySymbol]='HelloWorld';console.log(myObj[mySymbol]);//输出'HelloWorld'由于每个Symbol值都是唯一的，因此它们可以用于创建私有属性或方法，以避免命名冲突。例如，我们可以使用Symbol值作为对象的私有属性，如下所示：constmySymbol=Symbol('myPrivateProperty');classMyClass{constructor(){this[mySymbol]='Thisisaprivateproperty';}getPrivateProperty(){returnthis[mySymbol];}}constmyObj=newMyClass();console.log(myObj.getPrivateProperty());//输出'Thisisaprivateproperty'console.log(myObj[mySymbol]);//输出undefined，因为mySymbol是私有属性，无法直接访问除了使用Symbol()函数创建Symbol值外，还可以使用Symbol.for()函数创建全局共享的Symbol值。例如，我们可以使用Symbol.for()函数创建一个全局共享的Symbol值，并将其作为对象的属性键名，如下所示：constmyGlobalSymbol=Symbol.for('myGlobalSymbol');constmyObj={};myObj[myGlobalSymbol]='HelloWorld';console.log(myObj[myGlobalSymbol]);//输出'HelloWorld'注意，使用Symbol.for()函数创建的Symbol值会被添加到全局符号注册表中，可以在不同的代码文件中共享和访问11.迭代器在JavaScript中，Symbol是一种新的原始数据类型，它可以用来创建唯一的标识符，这些标识符可以用于对象的属性名或者其他需要唯一标识符的场合。而迭代器（Iterator）是一种对象，它提供了一种访问集合（如数组、对象和字符串等）中元素的方式，可以逐个访问集合中的元素，而不需要了解集合的内部实现。Symbol和迭代器之间的关系是，迭代器使用Symbol.iterator属性来定义自己的迭代器方法，该方法返回一个迭代器对象，该对象包含一个next()方法，用于迭代集合中的元素。举个例子，假设我们有一个数组，我们可以使用Symbol.iterator属性来定义一个迭代器方法，该方法返回一个迭代器对象，我们可以使用该对象的next()方法来逐个访问数组中的元素。下面是一个简单的例子：constarr=[1,2,3];constiterator=arr[Symbol.iterator]();console.log(iterator.next());//{value:1,done:false}console.log(iterator.next());//{value:2,done:false}console.log(iterator.next());//{value:3,done:false}console.log(iterator.next());//{value:undefined,done:true}在上面的例子中，我们首先获取了数组的迭代器对象，然后使用该对象的next()方法来逐个访问数组中的元素，直到所有元素都被访问完毕。每次调用next()方法时，都会返回一个包含当前元素值和是否遍历完成的对象。当遍历完成时，done属性为true，value属性为undefined需要注意的是，Symbol.iterator属性只能在可迭代对象上使用，即实现了@@iterator方法的对象。常见的可迭代对象包括数组、Set、Map、字符串等除了数组，ES6还为许多内置集合类型（如Set、Map等）和自定义对象提供了迭代器支持。我们可以使用for…of循环来遍历这些集合类型中的元素，例如：constmySet=newSet([1,2,3]);for(constvalueofmySet){console.log(value);}//Output:123constmyMap=newMap([['a',1],['b',2],['c',3]]);for(const[key,value]ofmyMap){console.log(key,value);}//Output:a1b2c3在这些例子中，我们使用for…of循环来遍历集合类型中的元素。由于这些集合类型都实现了迭代器，因此我们可以直接使用for…of循环来遍历它们中的元素，而不需要手动调用next()方法工作原理创建一个指针对象，指向当前数据解构的起始位置第一次调用对象的next方法，指针自动指向数据解构的第一个对象接下来不断调用next对象，指针一直往后移动，直到指向最后一个成员每调用next方法，返回一个包含value和done的属性的对象应用场景：需要自定义遍历数据的时候，要想到迭代器自定义遍历数据在JavaScript中，我们可以使用自定义迭代器来遍历数据。自定义迭代器是一个对象，它定义了一个next()方法，当调用next()方法时，它返回一个包含value和done属性的对象，value属性表示当前迭代到的值，done属性为true表示迭代结束，为false表示还有更多的值需要迭代下面是一个简单的例子，展示了如何使用自定义迭代器来遍历一个数组：constmyArray=[1,2,3];constmyIterator={[Symbol.iterator]:function(){letindex=0;return{next:function(){if(index&lt;myArray.length){return{value:myArray[index++],done:false};}else{return{done:true};}}};}};for(letvalueofmyIterator){console.log(value);//123}在这个例子中，我们定义了一个名为myIterator的对象，它包含一个Symbol.iterator方法，该方法返回一个包含next()方法的对象。next()方法返回一个包含当前迭代到的值和是否迭代结束的对象。我们可以使用for…of循环来遍历myIterator对象，每次迭代时都会调用next()方法，直到done属性为true为止回调函数回调函数是JavaScript中常见的一种编程模式，通常用于异步编程。一个回调函数就是一个函数，它作为参数传递给另一个函数，并且在该函数执行完毕后被调用。以下是一个简单的例子，演示了如何使用回调函数来处理异步操作：functionfetchData(callback){setTimeout(()=&gt;{constdata={name:"John",age:30};callback(data);},2000);}functiondisplayData(data){console.log(`Name:${data.name},Age:${data.age}`);}fetchData(displayData);这段代码的执行顺序和流程如下：首先，调用fetchData函数，并将displayData函数作为参数传递给它。在fetchData函数中，使用setTimeout函数模拟了一个异步操作，它会在2000毫秒后执行一个回调函数。在回调函数中，创建了一个包含name和age属性的对象，并将其作为参数传递给回调函数callback。fetchData函数执行完毕后，控制权被返回到调用它的地方，即主程序。在主程序中，fetchData函数的返回值为undefined，因为它没有显式返回任何值。2000毫秒后，setTimeout函数执行回调函数。回调函数中调用了displayData函数，并将包含name和age属性的对象作为参数传递给它。displayData函数被调用，它会在控制台中输出Name:John,Age:30。因此，整个程序的输出结果是Name:John,Age:30。12.PromisePromise的定义和使用Promise是ES6引入的异步编程的新解决方案，语法上Promise是一个构造函数，用来封装一部操作并可以获取其成功或失败的结果一个Promise必然处于以下三种状态之一：待定pending：初始状态，既没有被兑现，也没有被拒绝已兑现fulfilled：意味着操作成功完成已拒绝rejected：意味着操作失败Promise的使用：Promise构造函数newPromise((resolve,reject)=&gt;{})Promise.prototype.then()方法Promise.prototype.catch()方法当我们需要处理异步操作时，Promise是一种非常有用的工具。Promise是一个对象，它代表了一个异步操作的最终完成或失败，并且可以在完成或失败后返回一个值下面是一个使用Promise的例子：constpromise=newPromise((resolve,reject)=&gt;{//异步操作setTimeout(()=&gt;{constrandomNum=Math.random();if(randomNum&gt;0.5){resolve(randomNum);}else{reject('Randomnumberistoolow');}},1000);});promise.then((result)=&gt;{console.log(`Therandomnumberis${result}`);}).catch((error)=&gt;{console.log(`Error:${error}`);});在这个例子中，我们创建了一个Promise对象，并传入一个函数作为参数。这个函数接受两个参数：resolve和reject。当异步操作成功时，我们调用resolve函数并传入结果值。如果异步操作失败，则调用reject函数并传入错误信息接着，我们使用then方法来处理Promise对象的成功情况，并使用catch方法来处理Promise对象的失败情况。在then方法中，我们可以访问异步操作的结果值，并进行一些处理。在catch方法中，我们可以访问异步操作的错误信息，并进行一些处理在JavaScript中，我们可以使用Promise对象来封装Ajax请求。下面是一个简单的示例：functionajax(url){returnnewPromise(function(resolve,reject){varxhr=newXMLHttpRequest();xhr.open('GET',url);xhr.onload=function(){if(xhr.status===200){resolve(xhr.response);}else{reject(Error(xhr.statusText));}};xhr.onerror=function(){reject(Error('NetworkError'));};xhr.send();});}//调用示例ajax('https://jsonplaceholder.typicode.com/posts').then(function(response){console.log('成功获取到数据：',response);}).catch(function(error){console.log('获取数据失败：',error);});在上面的示例中，我们定义了一个名为ajax的函数，它接受一个URL参数，并返回一个Promise对象。在Promise构造函数中，我们使用XMLHttpRequest对象来发送Ajax请求，并在请求成功或失败时调用resolve或reject方法来改变Promise的状态。在then方法中，我们定义了成功获取数据后的回调函数，而在catch方法中，我们定义了获取数据失败后的回调函数</li>
  <li>第一章计算机网络体系结构相关概述计算机网络已经由一种通信基础设施发展成为一种重要的信息服务基础设施1网络、互联（连）网和因特网网路（Network）由若干结点（Node）和连接这些节点的链路（Link）组成。多个网络还可以通过路由器互连起来，这样旧构成了一个覆盖范围更大的网络，即互联网（或互连网）。因此，互联网是网络的网络（NetworkofNetworks）。因特网（Internet）是世界上最大的互连网络Internet和Internet的区别internet（互连网或互联网）是一个通用名词，它泛指由多个计算机网络互连而成的网络，在这些网络之间的通信协议是任意的。Internet（因特网）则是一个专用名词，它指当前全球最大的、开放的、由众多网络相互连接而成的特定计算机网络，它采用TCP/IP协议作为通信规则，其前身是美国的ARPANET。2因特网发展的三个阶段1969从单个网络ARPANET向互联网发展1985逐步建成三级结构的因特网1993逐步形成了多层次ISP结构的因特网 ISP(internetserviceprovider)译为互联网服务提供商，类似中国电信，中国移动，中国联通就是国内有名的ISP。ISP可以从互联网管理机构申请到很多IP地址，然后一些机构和个人从某个ISP获取IP地址的使用权，并可通过该ISP连接到互联网。 三层ISP结构分为主干ISP，地区ISP，本地ISP。本地ISP给用户提供最直接的服务，本地ISP可以连接到地区ISP，也可以连接到主干ISP。从原理上讲。只要每一个本地ISP都安装了路由器连接到某个地区ISP，而每一个地区ISP也有路由器连接到主干ISP，那么在这些相互连接的ISP的共同作用下，就可以完成互联网中的所有的分组转发任务。3因特网的标准化工作因特网的标准化工作对因特网的发展起到非常重要的作用。因特网在制定其标准上的一个很大的特点是面向公众。因特网所有的RFC（RequestForComments）技术文档都可以从因特网上下载；任何人可以随时使用电子邮件对某个文档发表意见或建议。因特网协会ISOC是一个国际性组织，它负责对因特网进行全面管理，以及在世界范围内促进其发展和作用。因特网体系结构委员会IAB，它负责对因特网有关协议的开发；因特网工程部IETF，负责研究中短期工程问题，主要针对协议的开发和标准化；因特网研究部IRTF，从事理论方面的研究和开发一些需要长期考虑的问题。指定因特网的正式标准要经过以下4个阶段因特网草案（这个阶段还不是RFC文档）建议标准（这个阶段开始就成为RFC文档）草案标准因特网标准4因特网的组成边缘部分由所有连接在因特网上的主机组成。这部分是用户直接使用的，用来进行通信（传送数据，音频或者视频）和资源共享。核心部分由大量网络和连接这些网路的路由器组成。这部分是为边缘部分提供服务的（提供连通性和交换）。1电路交换电话交换机接通电话线的方式称为电路交换从通信资源的分配角度来看，交换就是按照某种方式动态地分配传输线路的资源电路交换的三个步骤：建立连接（分配通信资源）通话（一直占用通信资源）释放连接（归还通信资源）2分组交换也称包交换，是将用户传送的数据划分成一定的长度，每个部分叫做一个分组，通过传输分组的方式传输信息的一种技术计算机网络的精确定义并未统一计算机网络的最简单的定义是：一些互相连接的、自治的计算机的集合互连：指计算机之间可以通过有线或无线的方式进行数据通信自治：指独立的计算机，它有自己的硬件和软件，可以单独运行使用集合：指至少需要两台计算机计算机网络较好定义：计算机网络主要是由一些通用的、可编程的硬件互连而成的，而这些硬件并非专门用来实现某一特定目的（例如，传送数据或视频信号）。这些可编程的硬件能够用来传送多种不同类型的数据，并能支持广泛的和日益增长的应用计算机网络所连接的硬件，并不限于一般的计算机，而是包括了智能手机等智能硬件计算机网络并非专门用来传送数据，而是能够支持很多种应用（包括今后可能出现的应用）计算机网络的分类按照交换技术分类电路交换网络报文交换网络分组交换网络按照使用者分类公用网专用网按照传输介质分类有线网络无线网络按照覆盖范围分类广域网WAN城域网MAN局域网LAN个域网PAN按照拓扑结构分类总线型网路星型网络环型网络网状型网络首先先了解一下比特的概念比特是计算机中数据量的单位，也是信息论中信息量的单位，一个比特就是二级制数字中的一个1或0（8bit=1Byte）速率连接在计算机网络上的主机，在数字信道上，传送比特的速率，也称为比特率或数据率带宽（两种含义）信号所包含的各种不同频率成分，所占据的频率范围，单位是：Hz（在模拟信号系统的意义）带宽是用来表示网络的通信线路所能传送数据的能力，因此网路带宽表示在单位时间内从网络中某一点到另一点所能通过的”最高数据率“，单位：b/s（在计算机网络中的意义）带宽的这两种表述之间有着密切的联系，一条通信线路的“频带宽度”越宽，其所传输数据的“最高数据率”也越高吞吐量吞吐量表示单位时间内通过某个网络（或信道、接口）的数据量另外，吞吐量受网络的带宽或额定速率的限制时延发送时延传播时延处理时延时延带宽积=传播时延*带宽（可以把链路想象成一个管道，长度为传播时延，横截面积为带宽，则时延带宽积就是该管道的体积）若发送端连续发送数据，则在所发送的第一个比特即将到达终点时，发送端就已经发送了时延带宽积个比特链路的时延带宽积又称为以比特为单位的链路长度往返时间RTT（Round-TripTime）利用率信道利用率：用来表示某信道有百分之几的时间是被利用的（有数据通过）网络利用率：全网络的信道利用率的加权平均丢包率也称分组丢失率，指在一定的时间范围内，传输过程中丢失的分组数量与总分组数量的比率另外分组丢失主要由两种情况：分组在传输过程中出现误码，被结点丢弃分组到达一台队列已满的分组交换机被丢弃；在通信量较大时就可能造成网络拥堵丢包率反应了网络的拥塞情况1常见的计算机网络体系结构OSI体系结构：（法律上的国际标准）应用层表示层会话层运输层网络层数据链路层物理层TCP/IP体系结构：（事实上的国际标准）应用层运输层网际层网络接口层计算机网络体系结构分层的必要性分层主要是为了将计算机之间的协调这个庞大的工程问题，转化为若干个较小的局部易于研究的问题其中：应用层：解决通过应用进程的交互来实现特定网络应用的问题运输层：解决进程之间基于网络的通信问题网络层：解决分组在多个网络上传输（路由）的问题数据链路层：解决分组在一个网络（或一段链路）上传输的问题物理层：解决使用何种信号来传输比特的问题计算机网络体系结构中的专业术语实体：任何可发送或接收信息的硬件或软件进程对等实体：接收双方相同层次中的实体协议：控制两个对等实体进行逻辑通信的规则的集合协议的三要素语法：定义所交换信息的格式语义：定义收发双发所要完成的操作同步：定义收发双方的时许关系服务：在协议的控制下，两个对等实体间的逻辑通信使得本层能够向上提供一层服务要实现本层协议，还需要使用下面一层所提供的服务协议是“水平”的，服务是“垂直”的实体看得见相邻下层所提供的服务，但并不知道实现该服务的具体协议，也就是说，下面的协议对上面的实体是透明的服务访问点：在同一系统中相邻两层的实体交互信息的逻辑接口，用于区分不同的服务类型服务原语：上层使用下层所提供的服务必须通过与下层交换一些命令，这些命令称为服务原语协议数据单元PDU：对等层次之间的传送数据包称为该层的协议数据单元服务数据单元SDU：同一系统内，层与层之间交互的数据包称为服务数据单元第二章物理层物理层考虑的是怎样才能在连接各种计算机的传输媒体上，传输数据比特流物理层为链路层屏蔽了各种传输媒体的差异，使数据链路层只需要考虑如何完成本层的协议和服务，而不必考虑网络具体的传输媒体是什么物理层协议的主要任务机械特性：指明接口所用接线器的形状和尺寸，引脚数目和排列、固定和锁定装置电气特性：指明在接口电缆的各条线上出现的电压的范围功能特性：指明某条线上出现的某一电平的电压表示何种意义过程特性：指明对于不同功能的各种可能事件的出现顺序物理层下面的传输媒体引导型传输媒体同轴电缆双绞线光纤电力线非引导型传输媒体无线电波微波红外线可见光物理层主要由三大类传输方式：第一类主要是串行传输和并行传输第二类主要是同步传输和异步传输同步传输采用收发双方时钟同步的方法·外同步：在收发双方之间添加一条单独的时钟信号线·内同步：发送端将时钟同步信号编码到发送数据中一起传输异步传输·字节之间异步（字节之间的时间间隔不固定）·字节中的每个比特仍然要同步（各比特的持续时间是相同的）第三类主要是单向通信（单工）、双向交替通信（半双工）、双向同时通信（全双工）编码和调制首先要知道一点，为了传输数据，都需要将数据转变为信号，这样才能转发出去把数据转换为模拟信号的过程称为调制，把数据转换为数字信号的过程称为编码信道极限容量信道的极限容量是指信道的最高码元传输速率因为信号在传输过程当中会不可避免的失真，造成信号衰弱，所以就由此引出奈氏准则和香农公式，它们的目的都是为了尽可能的提高码元传输速率，尽可能地降低信号失真奈氏准则（在假定理想条件下，为了避免码间串扰，码元传输速率是有上限的）理想低通信道的最高码元传输速率=2WBaud=2W码元/秒其中W表示信道带宽（单位为Hz），Baud表示波特（单位码元/秒）码元传输速率又称为波特率、调制速率、波形速率或符号速率。它与比特率有一定关系：1.当1个码元只携带1比特的信息量时，则波特率（码元/秒）与比特率（比特/秒）在数值上是相等的；2.当1个码元携带n比特的信息时，则波特率转换为比特率，数值上要乘以n要提高信息传输速率（比特率），就必须设法使每一个码元能携带更多个比特的信息量，这就要采用多元制实际的信道所能传输的最高码元速率，要明显低于奈氏准则给出的这个上限数值香浓公式（带宽受限且有高斯白噪声干扰的信道的极限信息传输速率）c=W*log2(1+S/N)c:信道的极限信息传输速率（b/s）W:信道带宽（Hz）S:信道内所传信号的平均功率N:信道内的高斯噪声功率S/N:信道比信道带宽或信道中信噪比越大，信息的极限传输速率越高在实际信道上能达到的信息传输速率要比该公式的极限传输速率低很多。因为还要受到很多其他因素的干扰第三章数据链路层链路（Link）就是从一个结点到相邻结点的一段物理线路，而中间没有任何其他的交换节点数据链路（DataLink）是指把实现通信协议的硬件和软件加到链路上，就构成了数据链路数据链路层以帧为单位传输和处理数据使用点对点信道的数据链路层要解决的三个问题：封装成帧、差错检测、可靠传输封装成帧封装成帧是指数据链路层给上层交付的协议数据单元添加帧头和帧尾使之成为帧帧头和帧尾中包含有重要的控制信息帧头和帧尾的作用之一就是帧定界透明传输是指数据链路层对上层交付的传输数据没有任何限制，就好像数据链路层不存在一样面向字节的物理链路使用字节填充的方法实现透明传输面向比特的物理链路使用比特填充的方法实现透明传输为了提高帧的传输速率，应当使帧的数据部分的长度尽可能大一些考虑到差错控制等多种原因，每一种数据链路层协议都规定了帧的数据部分的长度上限，即最大传送单元MTU（MaximumTransferUnit）差错检测实际的通信链路都是不理想的，比特在传输过程当中可能会产生差错，1可能变为0，0可能变为1，这就是比特差错在一段时间内，传输错误的比特所占传输比特总数的比率成为误码率BER（BitErrorRate）于是有两种常用的检测方式：奇偶校验和循环冗余校验CRC（CyclicRedundancyCheck）奇偶校验在待发送的数据后面添加1位奇偶校验位，使整个数据（包括所添加的校验位在内）中“1“的个数为奇数（奇校验）或偶数（偶校验）如果有奇数个位发生误码，则奇偶性发生变化，可以检查出误码如果有偶数个位发生误码，则奇偶性不发生变化，不能检查出误码（漏检）循环冗余校验收发双方约定好一个生成多项式G（x）发送方基于待发送的数据和生成多项式计算出差错检验码（冗余码），将其添加到待传输的数据后面一起传输接收方通过生成多项式来计算收到的数据是否产生了误码注意检错码只能检测出帧在传输过程中出现了差错，但不能定位错误，因此无法纠正错误要想纠正传输中的差错，可以使用冗余信息更多的纠错码进行前向纠错。但纠错码的开销比较大，在计算机网络中较少使用循环冗余校验CRC有很好的检错能力（漏检率非常低），虽然计算比较复杂，但非常易于用硬件实现，因此被广泛应用于数据链路层可靠传输使用差错检测技术（例如循环冗余CRC），接收方的数据链路层就可检测出帧在传输过程中是否产生了误码（比特错误）数据链路层向上提供的服务类型：不可靠传输服务：仅仅丢弃有误码的帧，其他什么也不做可靠传输服务：想办法实现发送端发送什么，接收端就接收什么无线链路易受干扰，误码率比较高，因此要求数据链路层必须向上层提供可靠传输服务停止-等待协议SW（Stop-and-Wait）接收端检测到数据分组有误码时，将其丢弃并等待发送方的超时重传，但对于误码率较高的点对点链路，为使发送方尽早重传，也可给发送放发送NAK分组为了让接收方能够判断所收到的数据分组是否是重复的，需要给数据分组编号。由于停止-等待协议的停止等待特性，因此只需要1个比特编号就够了，即编号0和1为了让发送方能够判断所收到的ACK分组是否是重复的，需要给ACK分组编号，所用比特数量与数据分组编号所用比特数量一样。数据链路层一般不会出现ACK分组迟到的情况，因此在数据链路层实现停止-等待协议可以不用给ACK分组编号回退N帧协议GBN（Go-Back-N）回退N帧协议在流水线传输的基础上利用发送窗口来限制发送方连续发送数据分组的数量，是一种连续ARQ协议。在协议的工作过程总发送窗口和接收窗口不断向前滑动，因此这类协议又叫做滑动窗口协议由于回退N帧协议的特性，当通信线路质量不好的时候，其信道利用率并不比停止-等待协议高接收方地接收窗口尺寸Wr的取值范围是Wr=1，因此接收方只能按序接收数据分组接收方只接收序号落在接收窗口内且无误码的数据分组，并且将接收窗口向前滑动一个位置，与此同时给发送方发回相应的确认分组。为了减少开销，接收方不一定每收到一个按序到达且无误码的数据分组就给发送方发回一个确认分组而是连续在收到好几个按序到达且无误码的数据分组后（由具体实现决定），才针对最后一个数据分组发送确认分组，这称为累积确认或者可以在字节有数据分组要发送时才对之前按序接收且无误码的数据分组进行捎带确认接收方收到未按序到达的数据分组，除丢弃外，还要对最近按序接收的数据分组进行确认选择重传协议SR（SelectiveRequest）接收窗口尺寸Wr的取值范围是1&lt;Wr&lt;=Wt接收方可接收未按序到达但没有误码并且序号落在接收窗口内的数据分组为了使发送方仅重传出现差错的分组，接收方不能再采用累积确认，而需要对每个正确接收到的数据分组再逐一确认接收方只有在按序接收数据分组之后，接收窗口才能向前相应滑动点对点协议PPPPPP协议为在点对点链路传输各种协议数据报提供了一个标准方法，主要由以下三个部分构成：对各种协议数据报的封装方法（封装成帧）链路控制协议LCP，用于建立、配置以及测试数据链路的连接一套网络控制协议NCPs，其中的每一个协议支持不同的网络层协议PPP帧的透明传输面向字节的异步链路使用字节填充法（插入转义字符）面向比特的同步链路使用的比特填充（零比特填充）PPP协议的工作状态媒体接入控制的基本概念共享信道要着重考虑的一个问题就是如何协调多个发送和接收站点对一个共享传输媒体的占用，即媒体接入控制MAC（MediumAccessControl）媒体介入控制分为：静态划分信道频分多址时分多址码分多址动态接入控制受控接入随机接入媒体接入控制——动态接入——随机接入——载波监听多址接入\碰撞检测CSMA/CD协议CSMA/CD协议地工作原理：多点接入MA：多个主机连接在一条总线上，竞争使用总线载波监听CS：发送帧前线检测总线，若总线空闲96比特时间，则立即发送；若总线忙，则持续检测总线直到总线空闲96比特时间后再重新发送碰撞检测到CD：边发送边检测碰撞，若检测到碰撞，则立即停止发送，退避一段随机时间后再重新发送使用CSMA/CD协议地以太网地争用期（碰撞窗口）发送帧地主机最多经过以太网端到端往返传播时延2ε这么长时间，就可检测到本次传输是否发生了碰撞，2ε称为争用期经过争用期这段时间还没有检测到碰撞，才能肯定这次发送不会发生碰撞以太网规定2ε的取值为512比特时间（即发送512比特所耗费的时间）使用CSMA/CD协议的以太网的最小帧长和最大帧长最小帧长=争用期*信道带宽（数据发送速率）以太网的最小帧长确保了主机可在帧发送完成之前就检测到该帧的发送过程中是否遭遇了碰撞，如果检测到碰撞，则停止发送帧的剩余部分，退避一段随机时间后，重新发送该帧为了防止主机长时间占用总线，以太网的帧也不能太长CSMA/CD协议曾经用于各种总线结构以太网和双绞线以太网的早期版本中，现在的以太网基于交换机和全双工连接，不会有碰撞，因此没必要使用CSMA/CD协议媒体接入控制——动态接入——随机接入——载波监听多址接入\碰撞避免CSMA/CA协议802.11无线局域网在MAC层使用CSMA/CA协议，以尽量减少碰撞发送的概率。不能使用CSMA/CD协议的原因是在无线局域网种无法实现碰撞检测。在使用CSMA/CA协议的同时，还使用停止-等待协议来实现可靠传输为了尽可能地避免各种可能地碰撞，CSMA/CA协议采用了一种不同于CSMA/CD协议地退避算法。当要发送帧地站点检测到信道从忙态转为空闲时，都要执行退避算法802.11标准规定，所有站在完成发送后，必须再等待一段帧间间隔时间才能发送下一帧。帧间间隔地长短取决于该站要发送地帧地优先级在802.11无线局域网地MAC帧首部种有一个持续字段，用来填入在本帧结束后还要占用信道多少时间，其他站点通过该字段可实现虚拟载波监听802.11标准允许要发送数据地站点对信道进行预约，即在发送数据帧之前先发送，请求发送RTS帧，在收到响应允许发送CTS帧后，就可发送数据帧MAC地址、IP地址以及ARP协议MAC地址是以太网地MAC子层所使用地地址—-数据链路层IP协议是属于TCP/IP体系结构网际层所使用地地址—网际层ARP协议属于TCP/IP体系结构地网际层，其作用是已知设备所分配到地IP地址，使用ARP协议可以通过IP地址获取到设备的MAC地址—-网际层MAC地址当多个主机连接在同一个广播信道上，要想实现两个主机之间的通信，则每个主机都必须有一个唯一的标识，即一个数据链路层地址在每个主机发送的帧中必须携带标识发送主机和接收主机的地址。由于这类地址是用于媒体接入控制MAC（MediaAccessControl），因此这类地址被称为MAC地址MAC地址一般被固化在网卡（网络适配器）的电可擦可编程只读存储器EEPROM中，因此MAC地址也被称为硬件地址MAC地址有时候也被称为物理地址（注意：这并不意味着MAC地址属于网络体系结构中的物理层）一般情况下，用户主机会包含两个网络适配器：有线局域网适配器（有线网卡）和无线局域网适配器（无线网卡）。每个网络适配器都有一个全球唯一的MAC地址，而交换机和路由器往往拥有更多的网络接口，所以会拥有更多的MAC地址。因此，严格来说，MAC地址是对网络上各种接口的唯一标识，而不是对网络上各设备的唯一标识IP地址（本身属于网络层，此处只介绍作用）IP地址是因特网（Internet）上的主机和路由器所使用的地址，用于标识两部分信息：网络编号：标识因特网上数以百万计的网络主机编号：标识同一网络上不同主机（或路由器）MAC地址不具备区分不同网络的功能如果只是一个单独的网络，不需要接入因特网，可以只使用MAC地址（不是一般用户的应用方式）如果主机所在的网络要接入因特网，即IP地址和MAC地址都要使用数据包转发过程中IP地址与MAC地址的变化情况：源IP地址和目的IP地址保持不变源MAC地址和目的MAC地址逐个链路（或逐个网络）改变地址解析协议ARP源主机在自己的ARP高速缓存表中查找目的主机的IP地址所对应的MAC地址，若找到了，则可以封装MAC帧进行发送；若找不到，则发送ARP请求（封装在单播MAC帧中），ARP响应中包含有目的IP和MAC地址源主机收到ARP响应后，将目的主机的IP地址与MAC地址记录到自己的ARP高速缓存表中，然后就可以封装之前想发送的MAC帧并发送给目的主机ARP的作用范围：逐段链路或逐个网络使用除ARP请求和响应外，ARP还有其他类型的报文ARP没有安全验证机制，存在ARP欺骗（攻击）问题集线器与交换机的区别集线器HUB早期以太网的互连设备工作在OSI体系结构的物理层对接收到的信号进行放大、转发使用集线器作为互连设备的以太网仍然属于共享总线式以太网。集线器互连起来的所有主机共享总线带宽，属于同一个碰撞域和广播域交换机SWITCH目前以太网中使用最广泛的互连设备工作在OSI体系结构的数据链路层（也包括物理层）根据MAC地址对帧进行转发使用交换机作为互连设备的以太网，称为交换式以太网。交换机可以根据MAC地址过滤帧，即隔离碰撞域交换机的每个接口是一个独立的碰撞域交换机隔离碰撞域但不隔离广播域（VLAN除外）以太网交换机自学习和转发帧的流程以太网交换机工作在数据链路层（包括物理层）以太网交换机收到帧后，在帧交换表中查找帧的目的MAC地址所对应的接口号，然后通过该接口转发帧以太网交换机是一种即用即插设备，刚上电启动时其内部的帧交换表是空的。随着网络中各主机间的通信，以太网交换机通过自学习算法自动逐渐简历起帧交换表以太网交换机自学习和转发帧的流程：收到帧后进行登记，登记的内容为帧的源MAC地址及进入交换机的接口号；根据帧的目的MAC地址和交换机的帧交换表对帧进行转发，有以下三种情况：明确转发：交换机知道应当从哪个（或哪些）接口转发该帧（单播、多播、广播）盲目转发：交换机不知道应当从哪个端口转发帧，只能将其通过除进入交换机的接口外的其他所有接口转发（也称为泛洪）明确丢弃：交换机知道不应该转发该帧，将其丢弃帧交换表中的每条记录都有自己的有效时间，到期删除，原因如下：交换机的接口该接了另一台主机主机更换了网卡以太网交换机的生成树协议STP以太网交换机使用生成树协议STP（SpanningTreeProtocol），可以在增加冗余链路来提高网络可靠性的同时又避免网络环路带来的各种问题不论交换机之间采用怎样的物理连接，交换机都能够自动计算并构建一个逻辑上没有环路的网络，其逻辑拓扑结构必须是树形的（无逻辑环路）最终生成的树形逻辑拓扑要确保连通整个网络当首次连接交换机或网络物理拓扑发生变化时（有可能是人为改变或故障），交换机都将进行生成树的重新计算虚拟局域网VLAN概述巨大的广播域会带来很多的弊端：广播风暴，难以管理和维护。因此人们想要分隔广播域，可以使用路由器隔离广播域，但其成本较高，因此虚拟局域网的技术诞生了虚拟局域网VLAN（VirtualLocalAreaNetwork）是一种将物理上分散的网络设备虚拟地组合成逻辑上的局域网的技术。它可以将不同的网络设备，如交换机、路由器、计算机等，按照其功能、位置、业务等因素分组，形成一个或多个虚拟局域网，实现不同VLAN之间的隔离和通信。这种技术可以提高网络的可靠性、安全性和灵活性，同时也可以减少网络的管理和维护成本虚拟局域网技术主要通过在交换机上进行VLAN配置来实现。交换机可以根据不同的标识（如VLANID）将其管理的端口分成不同的VLAN组，从而实现不同VLAN之间的隔离和通信。具体实现过程如下：配置VLAN：管理员可以在交换机上创建和配置不同的VLAN，为每个VLAN分配一个唯一的标识（VLANID）。配置端口：管理员可以将交换机的端口分配给不同的VLAN，在端口上设置VLANID，将端口与对应的VLAN关联起来。数据交换：当数据从一个VLAN中的设备发送到另一个VLAN中的设备时，数据包必须经过交换机来实现跨VLAN通信。交换机根据VLANID将数据包转发到对应的VLAN中的目的设备。VLAN间路由：如果需要在不同的VLAN之间进行通信，需要通过路由器来实现。路由器可以连接不同的VLAN，并在它们之间进行数据转发。通过这种方式，虚拟局域网技术可以实现不同VLAN之间的隔离和通信，提高网络的可靠性、安全性和灵活性。第四章网络层网络层的主要任务是实现网络互连，进而实现数据包在各个网络之间的传输网络层解决的主要问题：网络层向运输层提供怎样的服务（可靠、不可靠传输）网络层寻址问题路由选择问题网络层提供的两种服务对比方面虚电路服务数据报服务思路可靠通信应当由网络来保证可靠通信由用户主机来保证连接的建立必须建立网络层连接不需要建立网络层连接终点地址仅在连接建立阶段使用，每个分组使用短的虚电路号每个分组都有终点的完整地址分组的转发属于同一条虚电路的分组均按照同一路由进行转发每个分组可走不同的路由当结点出故障时所有通过出故障的结点的虚电路均不能工作出故障的结点可能会丢失分组，一些路由可能会发生变化分组的顺序总是按发送顺序到达终点到达终点时不一定按发送顺序服务质量保证可以将通信资源提前分配给每一个虚电路，容易实现很难实现IPv4地址概述IPv4地址就是，给因特网上的每一台主机（或路由器）的每一个接口分配一个，在全世界范围内是唯一的32比特的标识符IPv4地址的编址方法经历了三个阶段：分类编址—划分子网—无分类编址IPv4地址采用点分十进制表示方法分类编址的IPv4地址划分子网的IPv4地址为新增网络申请新的网络号会带来很多弊端：需要等待时间和花费更多的费用会增加其他路由器中路由表记录的数量浪费原有网络号中剩余的大量IP地址可以从主机号借用一部分比特作为子网号32比特的子网掩码可以表明分类IP地址的主机号部分被借用了几个比特作为子网号子网掩码使用连续的比特1来对应网络号和子网号子网掩码使用连续的比特0来对应主机号将划分子网的IPv4地址与其相应的子网掩码进行逻辑与运算就可得到IPv4地址所在子网的网络地址给定一个分类的IP地址和其相应的子网掩码，就可知道子网划分的细节：划分出的子网数量每个子网可分配的IP地址数量每个子网的网络地址和广播地址每个子网可分配的最小和最大地址默认的子网掩码是指再未划分子网的情况下使用的子网掩码A类：255.0.0.0B类：255.255.0.0C类：*************无分类编址的IPv4地址划分子网在一定程度上缓解了因特网在发展中遇到的困难，但是数量巨大的C类网因为其地址空间太小，没有得到充分的使用，而因特网的IP地址仍在加速消耗，整个IPv4地址空间面临全部消耗的威胁因此有了无分类域间路由选择CIDR（ClasslessInter-DomainRouting）CIDR取消了传统的A类、B类、C类地址，以及划分子网的概念CIDR可以更加有效的分配IPv4的地址空间CIDR使用斜线记法，即在IPv4地址后面加上”/“，在斜线后面写上网络前缀所占的比特数量CIDR实际上是将网络前缀都相同的连续的IP地址组成一个“CIDR地址块”路由聚合（也称超网）的方法是找共同前缀网络前缀越长，地址块越小，路由越具体若路由器查表转发分组时发现有多条路由可选，则选择网络前缀最长的那条，这称为最长前缀匹配，因为这样的路由更具体IP数据报的发送和转发过程主机发送IP数据报判断目的主机是否与自己在同一个网络：若在同一个网络，则属于直接交付若不在同一个网络，则属于间接交付，传输给主机所在的网络的默认网关（路由器），由默认网关帮忙转发；路由器转发IP数据报检查IP数据报首部是否出错：若出错，则直接丢弃该IP数据报并通告源主机若没有出错，则进行转发根据IP数据报的目的地址在路由表中查找匹配的条目：若找到匹配的条目，则转发给条目中指示的下一跳若找不到，则丢弃该IP数据报并通告源主机路由选择协议概述它分为静态和动态路由选择两类静态路由选择由人工培植的网络路由、默认路由、特定主机路由、黑洞路由等都属于静态路由这种人工配置方式简单、开销小，但不能及时适应网络状态（流量、拓扑等）的变化一般只在小规模网络中采用动态路由选择路由选择器通过路由选择自动获取路由信息当一个数据包需要从源设备传输到目的设备时，路由器需要根据路由表中存储的路由信息选择最佳的路径将数据包传输到目的地。路由选择器通过交换路由信息，更新路由表，选择最优路径，从而实现网络中的数据传输。路由选择器通过交换路由信息的方式，可以自动获取网络中的路由信息，包括网络拓扑结构、路由器之间的连接状态、网络中的子网等信息。它可以将这些信息传递给其他路由器，更新路由表，并根据最新的路由信息选择最佳的路径进行数据转发。比较复杂，开销大，能较好的适应网路状态的变化适用于大规模网路路由信息协议RIP的基本工作原理路由信息协议RIP（RoutingInformationProtocol）是内部网关协议IGP中最先得到广泛使用的协议之一RIP要求自治系统AS内的每一个路由器都要维护从它自己到AS内其他每一个网络的距离记录。这是一组距离，称为“距离向量”RIP使用“跳数”来衡量到达目的网络的距离路由器到直连网络的距离定义为1路由器到非直连网络的距离定义为所经过的路由器数目+1允许一条路径最多只能包含15个路由器，“距离”等于16时，表示不可达。因此，RIP只适用于小型互联网RIP认为好的路由就是“距离短”的路由，也就是所通过路由器数量最少的路由当到达同一目的网络的网络有多条“距离相等”的路由时，可以进行等价负载均衡RIP包含以下三个要点：和谁交换信息仅和相邻路由器交换信息交换什么信息自己的路由表何时交换信息周期性交换RIP的基本工作过程：路由器刚开始工作时，只知道自己到直连网络的距离是1每个路由器仅和相邻路由器周期性交换并更新路由信息若干次交换和更新后，每个路由器都知道到达本AS内个网络的最短距离和下一跳地址，称为收敛RIP的路由条目的更新规则*：发现了新网络，添加到达目的网络，相同下一跳，最新消息，更新到达目的网络，不同下一跳，新路由优势，更新到达目的网络，不同下一跳，新路由劣势，不更新到达目的网络，不同下一跳，等价负载均衡RIP存在“坏消息传播的慢”的问题坏消息传播的慢，又称为路由环路或距离无穷计数问题，这是距离向量算法的一个固有问题距离向量算法（DistanceVectorAlgorithm）是一种用于计算路由器之间最短路径的算法。它是一种分布式算法，每个路由器只知道自己与相邻路由器之间的距离和路径信息，通过交换路由表信息，逐步更新整个网络中的路由表，最终得到每个路由器到其他路由器的最短路径。距离向量算法的基本思想是，每个路由器维护一个距离向量表，记录到达网络中每个目的地的距离和路径。路由器通过将自己的距离向量表发送给相邻路由器，接收相邻路由器的距离向量表，并根据收到的信息更新自己的距离向量表。路由器之间周期性地交换距离向量表，直到整个网络的路由表收敛到最优解。距离向量算法的优点是实现简单、计算速度快，适用于小型网络。但是它也有一些缺点，比如容易出现路由环路问题、收敛速度慢、路由器需要周期性地更新路由表等。因此，在大型网络中，通常采用链路状态路由算法（LinkStateAlgorithm）来计算最短路径，它可以更准确地计算网络中的路径，并且具有更好的可扩展性和稳定性。开放最短路径优先OSPF的基本工作原理开放最短路径优先OSPF（OpenShortestPathFirst）是为克服RIP的缺点发明出来的“开放”表明OSPF协议不是受某一家厂商控制，而是公开发表的“最短路径优先”是因为使用了迪杰斯特拉提出的最短路径算法SPFOSPF是基于链路状态的，采用SPF算法计算路由，从根本上解决了不会产生路由环路，并且它不限制网络规模，更新效率高，收敛速度快链路状态是指本路由器都和哪些路由器相邻，以及相应链路的代价“代价”用来表示费用、距离、时延、带宽等等，这些都是由网络管理人员来决定使用OSPF的每个路由器都会产生链路状态通告LSA（LinkStateAdvertisement），LSA包含以下内容：直连网络的链路状态信息邻居路由器的链路状态信息LSA被封装在链路状态更新分组中，采用洪泛法发送OSPF有物种分组类型：问候分组数据库描述分组链路状态请求分组链路状态更新分组OSPF在多点接入网络中路由器邻居关系的建立为了使OSPF能够用于规模很大的网络，OSPF把一个自治系统再划分为若干个更小的范围，叫做区域划分区域的好处就是把利用洪泛法交换链路状态信息的范围局限于每一个区域而不是整个自治系统，这就减少了整个网络上的通信量边界网关协议BGP的基本工作原理外部网关协议EGP在不同的自治系统内，度量路由的“代价”（距离、带宽、费用等）可能不同。因此，对于自治系统之间的路由选择，使用“代价”作为度量来寻找最佳路由是不行的BGP只是力求寻找一条能够到达目的网络且比较好的路由（不能兜圈子），而并非要寻找一条最佳路由再配置BGP时，每个自治系统的管理员要选择至少一个路由器作为该自治系统的“BGP发言人”不同自治系统的BGP发言人要交换路由信息，必须建立TCP连接，端口号是179在此TCP连接上交换BGP报文以建立BGP会话利用BGP会话交换路由信息使用TCP连接交换路由信息的两个BGP发言人，彼此称为对方的临站或对等站BGP发言人除了运行BGP外，还必须运行自己所在自治系统所使用的内部网关协议IGPBGP发言人交换网络可达性的信息（要到达某个网络索要经过的一系列自治系统）当BGP发言人互相交换了网络可达性的信息后，各BGP发言人就根据所采用的策略从收到的路由信息中找出到达各自治系统的较好的路由。也就是构造出树形结构，不存在回路的自治系统连通图BGP适用于多级结构的因特网BGP-4有以下四种报文：OPEN报文：用来与相邻的另一个BGP发言人建立关系，是通信初始化UPDATE报文：用来通告某一路由的信息，以及列出要撤销的多条路由KEEPALIVE报文：用来周期性地证实临站地连通性NOTIFICATION报文：用来发送检测到的差错网际控制报文协议ICMP为了更有效地转发IP数据报和提高交付成功地机会，在网际层使用了ICMP（InternetControlMessageProtocol）主机或路由器使用ICMP来发送差错报告报文和询问报文ICMP报文被封装在IP数据报中发送ICMP差错报告报文共有以下五种：终点不可达源点抑制时间超过参数问题改变路由（重定向）以下情况不应该发送ICMP差错报告报文：对第一个分片的数据报片的所有后续数据报片都不发送ICMP差错报告报文对具有多播地址的数据报都不发送对具有特殊地址（*********或0.0.0.0）的数据报不发送常用的ICMP询问报文有两种：回送请求和回答时间戳请求和回答ICMP应用：分组网间探测ping跟踪路由traceroute虚拟专用网VPN与网络地址转换NAT虚拟专用网VPN（VirtualPrivateNetwork）利用公用的因特网作为本机构各专用网之间的通信载体，这样的专用网又称为虚拟专用网同一机构内不同部门的内部网络所构成的虚拟专用网VPN又称为内联网VPNVPN要保证传输数据的安全性，会将原始的内部数据报进行加密，然后再将其封装称为在因特网上发送到的外部数据报有时一个机构的VPN需要有些外部机构（合作伙伴）参加进来，这样的就称为外联网VPN在外地工作的员工需要访问公司内部的专用网络，通过VPN软件就可以访问，这种称为远程接入VPN网络地址转换NAT（NetworkAddressTranslation）由于IP地址的紧缺，一个机构能够申请到的IP地址数量往往小于本机构所拥有的主机数量。因此，虚拟专用网中的各主机所分配的地址应该是本机构可自由分配的专用地址，而不是需要申请的、在因特网上使用的公有地址NAT能使大量，使用内部专用红地址的专用网络用户共享少量外部全球地址，来访问因特网上的主机和资源由于绝大多数的网络应用都是使用运输层协议TCP或UDP来传送数据，因此可以利用运输层的端口号和IP地址一起进行转换。这样，网络地址与端口号转换NAPT（Network　Address　andPortTranslation）由于NAT对外网屏蔽了内网主机的网络地址，能为内网的主机提供一定的安全保证第五章运输层计算机网络体系结构中的物理层、数据链路层、以及网络层它们共同解决了主机将通过异构网络互联起来所面临的问题，实现了主机到主机的通信但实际上在计算机网络中进行通信的真正实体是位于通信两端主机中的进程运输层的任务是给运行在不同主机上的应用进程提供直接的通信服务，运输层协议又称为端到端协议运输层向高层用户屏蔽了下面网络核心的细节（网络拓扑、路由选择协议等），它使应用进程看见的就好像是在两个运输层实体之间有一条端到端的逻辑通信信道运输层提供两种运输协议，即面向连接的TCP和无连接的UDP运输层中端口号、复用和分用的概念运输层端口号是指在计算机网络中，用于标识应用程序或服务的端口号。每个端口号都与一个特定的应用程序或服务相关联。如HTTP服务使用的端口号是80，FTP服务使用的端口号是21复用（Multiplexing）是指在发送端将多个应用程序的数据流合并成一个数据流进行传输，而在接收端将这个数据流分解成多个应用程序的数据流。这样可以节省网络资源，提高网络的利用率。在TCP/IP协议中，复用是通过端口号来实现的。发送端将数据包发送到目的地址的相应端口号，接收端根据端口号将数据包分发给对应的应用程序。分用（Demultiplexing）是指在接收端将一个数据流分解成多个应用程序的数据流。接收端根据端口号将接收到的数据包分发给对应的应用程序。在TCP/IP协议中，分用是通过端口号来实现的。接收端根据端口号将接收到的数据包分发给对应的应用程序。UDP和TCP的对比用户数据报协议UDP（UserDATagramProtocol）无连接支持一对一，一对多，多对一和多对多交互通信对应用层交付的报文直接打包尽最大努力交付，也就是不可靠；不使用流量控制和拥塞控制首部开销小，仅8字节传输控制协议TCP（TransmissionControlProtocol）面向连接每一条TCP连接只能有两个端点，只能是一对一通信可靠传输，使用流量控制和拥塞控制首部最小20字节，最大60字节TCP的流量控制一般来说，我们是希望数据传输的更快一些但如果发送方把数据发送得过快，接收方就可能来不及，这就会造成数据的丢失所谓流量控制（flowcontrol）就是让发送方的发送速率不要太快，要让接收方来得及接收利用滑动窗口机制，可以很方便地在TCP连接数实现对放松方地流量控制TCP接收方利用自己的接收窗口大小来限制发送方发送窗口的大小TCP发送方收到接收方的零窗口通知后，应启动持续计时器。持续计时器超时后，向接收方发送零窗口探测报文TCP的拥塞控制TCP拥塞控制是一种网络流量控制机制，旨在避免网络拥塞并保证数据传输的可靠性。TCP拥塞控制包括四个主要算法：慢开始、拥塞避免、快重传和快恢复慢开始（SlowStart）：慢开始算法的主要目的是在数据传输开始时缓慢增加发送方的拥塞窗口（cwnd），以避免网络拥塞。发送方在开始传输数据时，将拥塞窗口设置为一个较小的值，然后每收到一个确认消息就将拥塞窗口增加一倍，直到达到一个阈值（通常是拥塞窗口的一半）。这个阈值称为慢开始阈值（ssthresh）拥塞避免（CongestionAvoidance）：拥塞避免算法的主要目的是在网络拥塞时减少发送方的拥塞窗口，以避免网络拥塞的进一步恶化。发送方在达到慢开始阈值后，将拥塞窗口增加一个拥塞窗口的倒数（即每个确认消息增加1/cwnd），这样就可以缓慢增加发送方的拥塞窗口，避免网络拥塞快重传（FastRetransmit）：快重传算法的主要目的是在网络丢包时快速重传丢失的数据包，以减少数据传输的延迟。如果发送方连续收到三个重复的确认消息，就会认为有一个数据包丢失了，并立即重传该数据包，而不必等待超时快恢复（FastRecovery）：快恢复算法的主要目的是在网络丢包时快速恢复发送方的拥塞窗口，以减少数据传输的延迟。如果发送方连续收到三个重复的确认消息，就会认为有一个数据包丢失了，并将拥塞窗口设置为慢开始阈值的一半，然后进入快恢复状态。在快恢复状态下，发送方每收到一个确认消息就将拥塞窗口增加1，直到达到慢开始阈值TCP可靠传输实现TCP基于以字节为单位的滑动窗口来实现可靠传输发送方在未收到接收方的确认时，可将发送窗口内还未发送的数据全部发送出去接收方只接收序号落入发送窗口内的数据虽然发送方的发送窗口是根据接收方的接收窗口设置的，但在同一时刻，发送方的发送窗口并不总是和接收方的接收窗口一样大网络传送窗口值需要经历一定的时间滞后，并且这个时间不确定发送方还可能根据网络当时的拥塞情况适当减小自己的发送窗口尺寸对于不按序到达数据应如何处理，TCP无明确规定如果接收方把不按序到达的数据一律丢弃，那么接收窗口的管理系那个会比较简单，但这样对网络资源利用不好，因为发送方会重复传送较多的数据TCP通常对不按序到达的数据是先临时存在接收窗口上，等到字节流中所缺少的字节收到后，再按序交付上层的应用进程TCP要求接收方必须要有累积确认和捎带确认机制，这样可以减小传输开销。接收方可以再合适的时候发送确认，也可以再字节有数据要发送时把确认信息顺便捎带上接收方不应过分推迟发送确认，否则会导致发送方不必要的超时重传捎带确认实际上并不经常发生，因为大多数应用程序很少同时再两个方向上发送数据TCP的通信是全双工通信通信中的每一方都在发送和接收报文段。因此，每一方都有自己的发送窗口和接收窗口TCP的运输连接管理——TCP的连接建立TCP是面向连接的协议，它基于运输连接来传送TCP报文段TCP运输连接的建立和释放是每一次面向连接的通信中必不可少的过程TCP运输连接有三个阶段：建立TCP连接数据传送释放TCP连接三次握手是TCP协议中建立可靠连接的过程，它由客户端和服务器之间的三个步骤组成。这三个步骤分别是：第一次握手：客户端向服务器发送一个SYN（同步）数据包，其中包含一个随机生成的序列号（Seq）第二次握手：服务器收到客户端的SYN数据包后，向客户端回复一个SYN+ACK（同步、确认）数据包，其中包含确认号（ack）和一个随机生成的序列号（Seq）第三次握手：客户端收到服务器的SYN+ACK数据包后，向服务器回复一个ACK（确认）数据包，其中包含确认号（ack），此时TCP连接已经建立三次握手的目的是确保客户端和服务器之间的TCP连接是可靠的，防止重复连接和数据包丢失等问题。通过这个过程，客户端和服务器之间可以互相确认彼此的身份，并建立起一个可靠的连接来传输数据TCP的运输连接管理——TCP的连接释放四次挥手是TCP连接的断开过程，由客户端和服务器共同完成。四次挥手的过程如下：客户端发送一个FIN报文给服务器，表示客户端已经没有数据要发送了服务器接收到FIN报文后，发送一个ACK报文给客户端，表示服务器已经接收到了客户端的FIN报文服务器发送一个FIN报文给客户端，表示服务器已经没有数据要发送了客户端接收到服务器的FIN报文后，发送一个ACK报文给服务器，表示客户端已经接收到了服务器的FIN报文这样四次挥手过程完成后，TCP连接就被正常地关闭了。需要注意的是，四次挥手过程中，每个报文都需要对方发送确认，因此需要四次挥手才能完成连接的断开第六章应用层应用层是计算机网络体系结构的最顶层，是设计和建立计算机网络的最终目的客户/服务器方式（C/S方式）和对等方式（P2P方式）客户/服务器（C/S方式）客户和服务器是指通信中所涉及的两个应用进程客户/服务器方式所描述的是进程之间服务和被服务的关系客户是服务请求方，服务器是服务提供方服务器总是处于运行状态，并等待客户的服务请求。服务器具有固定的端口号（HTTP服务器的默认端口号是80）基于C/S方式的应用服务器通常是服务集中型的，即应用服务集中在，网络中比客户计算机少得多的服务器计算机上由于一台服务器计算机要为多个客户机提供服务，在C/S应用中，常会出现服务器计算机跟不上众多客户机请求的情况在C/S应用中，常用计算机集群（或服务器场）构建一个强大的虚拟服务器对等（Peer-to-Peer，P2P）方式在P2P方式中，没有固定的服务请求者和服务提供者，分布在网络边缘各端系统中的应用进程是对等的，被称为对等方。对等方相互之间直接通信，每个对等方既是服务的请求者，又是服务的提供者基于P2P的应用是服务分散型的，因为服务不是集中在少数几个服务器计算机中，而是分散在大量对等计算机中，这些计算机并不为服务提供商所有，而是为个人控制的桌面计算机和笔记本电脑P2P方式的最突出特性之一就是它的可扩展性，因为系统每增加一个对等方，不仅增加的是服务的请求者，同时也增加了服务的提供者，系统性能不会因规模的增大而降低P2P方式具有成本上的优势，因为不需要旁大的服务器设施和服务器带宽动态主机配置协议DHCP动态主机配置协议（DynamicHostConfigurationProtocol，简称DHCP）是一种网络协议，用于自动分配IP地址、子网掩码、默认网关等网络参数，以及DNS服务器等信息给网络中的设备。DHCP服务器可以自动为新加入网络的设备分配可用的IP地址，从而避免了手动配置IP地址的繁琐过程。DHCP还可以对已有的设备进行IP地址的更新、续约和释放等操作，从而实现IP地址的动态管理。DHCP协议通常运行在局域网中的DHCP服务器上，而客户端设备则通过DHCP协议来获取网络配置信息。域名系统DNS（Domain　Name　System）域名系统DNS是因特网使用的命名系统，用来把便于人们记忆的具有特定含义的主机名，转换为便于机器处理的IP地址因特网采用层次树状结构的域名系统顶级域名TLD（TopLevelDomain）分为以下三类：国家顶级域名nTLD通用顶级域名给TLD反向域arpa域名和IP地址的映射关系必须保存在域名服务器中，供所有其他应用查询。但是不可能将所有信息都存储在一台域名服务器中，DNS使用分布在各地的域名服务器来实现域名到IP地址的转换域名服务器可以划分为以下四种类型：根域名服务器顶级域名服务器权限域名服务器本地域名服务器域名解析的过程有两种查询方式：递归查询迭代查询为了提高DNS的查询效率，并减轻根域名服务器的负荷和减少因特网上的DNS查询报文数量，在域名服务器和主机中广泛地使用了高速缓存DNS报文使用运输层地UDP协议进行封装，运输层端口号是53文件传输协议FTP将某台计算机中地文件通过网络传送到可能相距很远地另一台计算机中，是一项基本的网络应用，即文件传送文件传输协议FTP（FileTransferProtocol）是因特网上使用的最广泛地文件传输协议FTP提供交互式地访问，允许客户指明文件地类型与格式，并允许文件具有存取权限FTP屏蔽了各计算机系统地细节，因而适合于在异构网络中任意计算机之间传输文件FTP客户和服务器之间要建立以下两个并行的TCP连接：控制连接，在整个会话期间一直保持打开，用于传送FTP相关控制命令数据连接，用于文件传输，在每次文件传输时才建立，传输结束就关闭默认情况下，FTP使用TCP21端口进行控制连接，TCP20端口进行数据连接。是否使用TCP20端口建立数据连接与传输模式有关，主动方式使用TCP20端口，被动方式由服务器和客户端自行协商决定电子邮件电子邮件系统采用客户/服务器方式，三个主要组成构件：用户代理、邮件服务器、所需要的协议用户代理是用户与电子邮件系统的接口，又称为电子邮件客户端软件邮件服务器是电子邮件系统的基础设施。因特网上所有的ISP都有邮件服务器，其功能是发送和接收邮件，同时还要负责维护用户的邮箱协议包括邮件发送协议（SMTP）和邮件读取协议（POP3）最常用的邮件发送协议是简单邮件传送协议SMTP基于TCP连接，端口号为25只能传送ASCII码文本用于用户代理向邮件服务器发送邮件以及邮件服务器之间的邮件发送为解决SMTP传送非ASCII码文本的问题，提出了多用途因特网邮件扩展MIME常用的邮件读取协议有以下两个：邮局协议POP3：非常简单、功能有限的邮件读取协议。用户只能以下载并删除方式或下载并保留方式从邮件服务器下载邮件到用户方计算机。不允许用户在邮件服务器上管理自己的邮件、因特网邮件访问协议IMAP：功能比POP3强大的邮件读取协议。用户在自己的计算机上就可以操控邮件服务器中的邮件，IMAP是一个联机协议POP3和IMAP4都采用基于TCP连接的客户/服务器方式，POP3使用端口110，IMAP使用端口143基于万维网的电子邮件通过浏览器登录这种工作模式在用户浏览器与邮件服务器网站之间使用HTTP协议，而邮件服务器之间使用SMTP协议万维网WWW万维网WWW（WorldWideWeb）是一个大规模的、联机式的信息储藏所，是运行在因特网上的一个分布式应用浏览器最重要的部分是渲染引擎，也就是浏览器内核，负责对网页内容进行解析和显示万维网使用统一资源定位符URL来指明因特网上任何种类资源的位置万维网文档超文本标记语言HTML：使用多种“标签”来描述网页的结构和内容（网页扩展名为.html）层叠样式表CSS：美化网页样式（文件扩展名为.css）脚本语言JavaScript：控制网页的行为（文件扩展名为.js）超文本传输协议HTTP（HyperTextTransferProtocol）定义了浏览器（即万维网客户进程）怎样向万维网服务器请求万维网文档，以及万维网服务器怎样把万维网文档传送给浏览器HTTP/1.0采用非持续连接方式，每次浏览器要请求一个文件都要与服务器建立TCP连接（80端口），当收到响应后就立即关闭连接HTTP/1.1采用持续连接方式，万维网服务器在发送响应后仍然保持这条连接，使同一个客户（浏览器）和该服务器可以继续在这条连接上传送后续的HTTP请求报文和响应报文。为了进一步提高效率，还可以采用流水线方式，即浏览器在收到HTTP的响应保温之前就能够连续发送多个请求和报文HTTP有两类报文，请求报文和响应报文Cookie提供了一种机制使得万维网服务器能够“记住”用户，无需用户主动提供用户标识信息，也就是说，Cookie是一种对于无状态的HTTP进行状态化的技术在万维网中，可以使用缓存机制来提高万维网的效率，万维网缓存又称为Web缓存，可位于客户机，也可位于中间系统上，位于中间系统上的Web缓存又称为代理服务器第七章网络安全安全威胁被动攻击主动攻击中断篡改伪造恶意程序：1）计算机病毒2）计算机蠕虫3）特洛伊木马4）逻辑炸弹5）后门入侵6）流氓软件拒绝服务安全服务保密性：确保网络中传输地信息只有其发送方和接收方才能懂得其含义，而信息地截获者则看不懂所截获地数据。保密性是计算机网络中最基本地安全服务，也是对付被动攻击所必须具备地功能报文完整性：确保网络中传输地信息不被攻击篡改或者伪造，它在应对主动攻击时是必不可少地实体鉴别：通信两端地实体能够相互验证对方地真实身份，确保不会与冒充者进行通信。不可否认性：来防止发送方或接收方否认发送或接收过某种信息访问控制：可以限制和控制不同实体对信息源或其他系统资源进行访问的能力，必须在鉴别实体可用性：是确保授权用户能够正常访问系统信息和资源。很多攻击都会导致系统可用性的损失，拒绝服务Dose攻击就是可用性最直接的威胁DoS攻击是指攻击者通过向目标系统发送大量的请求或数据流量，使其超过正常处理能力，从而导致系统崩溃或无法正常工作。DoS攻击通常使用单个计算机或网络连接来发起攻击DDoS攻击是一种更复杂的攻击形式，它涉及多个计算机或网络连接，同时向目标系统发送大量的请求或数据流量。攻击者通常使用僵尸网络（也称为“僵尸军团”）来发起攻击，这是一组已被攻击者控制的计算机，可以在攻击者的指令下同时向目标系统发送请求或数据流量密码学相关概念将发送的数据变换成对任何不知道如何做逆变换的人都不可理解的形式，从而保证数据的机密性，这种变换称为加密加密前的数据被称为明文加密后的数据被称为密文通过某种逆变换将密文重新变换为明文，这种逆变换称为解密加密和解密过程可以使用密钥作为参数密钥必须保密，但加密和解密的过程可以公开只有知道密钥的人才能解密密文，否则即使知道加密或解密算法也无法解密密文另外，加密密钥和解密密钥可以相同，也可以不同（即使不同，这两个密钥也必然有某种相关性），这取决于采用的是对称密钥密码体制还是公开密钥密码体制对称密钥密码体制对称密钥密码体制是指加密密钥与解密密钥相同的密码体制数据加密标准DES，是对称密钥密码体制的典型代表公钥密码体制公钥密码体制使用不同的加密密钥和解密密钥加密密钥是向公众公开的，称为公钥pk解密密钥是需要保密的，称为私钥SK加密算法D和解密算法D都是公开的常见的网络攻击及防范网络扫描网络扫描是获取攻击目标信息的一种重要技术攻击目标信息包括目的主机的IP地址、操作系统、运行的程序、存在的漏洞等在进行网络攻击之前，对攻击目标的信息掌握的越全面和具体,就能合理有效地制定攻击策略和方法，进而提高网络攻击地成功率网络扫描只要有四种类型：主机发现：指搜索要攻击的主机，实际要确定该目标主机的IP地址，主要利用ICMP网际控制报文协议端口扫描：获取目标主机所有端口的工作状态，进而确定目标主机开放了哪些网络服务操作系统检测：是指通过特定的技术手段，获取用户设备上运行的操作系统类型及版本号等信息的过程漏洞扫描：指通过自动化工具或手动方式，对计算机系统、网络设备、应用程序等进行扫描，以发现其中存在的漏洞网络监听网络中传输的数据大部分都是明文形式，如果攻击者对网络进行监听并截获了包含有大量明文信息的一系列分组，则可从这些分组中直接分析出账号、密码等敏感信息常见的网络监听类型有：分组嗅探器：（PacketSniffer）是一种网络安全工具，可以截获网络通信过程中的数据包，并对其进行分析和解码。它可以用于网络流量监控、协议分析、网络故障排查、网络安全检测等方面。分组嗅探器通常以软件形式存在，可以在计算机上安装和运行，也可以作为网络设备的一部分来使用。但需要注意的是，未经授权使用分组嗅探器可能会涉及到违法行为，需要遵守相关法律法规交换机毒化攻击：一种利用网络交换机漏洞或者误配置，通过发送特定的网络数据包，使得交换机中的MAC地址表被篡改，从而导致网络中的通信数据被劫持、篡改、丢失等问题的攻击行为。攻击者可以通过伪造MAC地址，将自己的MAC地址伪装成网络中其他设备的MAC地址，从而实现对网络流量的监控和劫持ARP欺骗：（ARPspoofing）是一种网络攻击技术，攻击者通过伪造ARP协议中的欺骗信息，将自己的MAC地址伪装成目标主机的MAC地址，从而使得攻击者可以截取目标主机与其他主机之间的通信，或者将流量重定向到攻击者所控制的主机上。ARP欺骗攻击常被用于网络钓鱼、中间人攻击等恶意行为中。为防止ARP欺骗攻击，可以采取一些防范措施，比如使用静态ARP表、ARP检测工具、网络流量监控等手段。</li>
  <li>距离2022年结束还有两天，回想这一年，真的是发生了太多的事情，今年真的可以用“魔幻”和“仓促”这两个词来形容。这一年所发生的所有的高兴和不开心真的也只有自己能够体会，很幸运我今年活下来了。为什么这么说呢，上个月的这个时候，疫情还十分严重，身边的朋友一个接一个地被拉到隔离的地方，谁能够想到一个月之后全国久彻底放开了。本来这是一件值得开心的事情，可随之而来的就是变异的病种，病毒肆意传播，很快，周围基本所有人都中招了。发烧、浑身无力、咳嗽不止…这真的不是一个小感冒，每天都可以从网上看到，小孩老人因此而去世了，有朋友告诉我她由阳转阴之后，偶然去医院做了个肺部检查，发现有阴影，还好早早发现治疗了，不然后果真的不敢想。上半年的时候，那时候疫情还不是很严重，我回到了学校，等待考研结果和国家线看看是否有机会，很不幸，我的分数没有到达目标院校的复试线，因此，考研宣告失败，我花了几天去收拾自己的心情，赶紧准备进行下一阶段，是选择二战还是选择就业。生活又给了我一记耳光，廊坊的疫情突然加重，我们学校周围出现了病例，学校要求所有学生宿舍学习，这也意味着我没有办法出去实习，之前的计划再次被打乱，我只好也只能在宿舍学习。也是在这个时候，我做好了二战的打算。因为马上要面临毕业，所以这几个月的时间我也要花费很多时间在自己的毕业设计和论文上面，花费了两个月左右的时间，终于完成了毕业设计，基于Java的旅游网站的开发，然后着手完善论文，时间一天又一天的过去，很快时间来到了5月份，论文做了最后的完善和差重，开始了答辩。答辩很顺利地通过了，这个时候疫情已经减轻了，我们也被允许可以自由出入宿舍，终于可以自由活动了。不过时间真的过的很快，所有跟毕业相关的事情已经基本处理完毕，大家都已经准备要离开学校了，我也一样，在和一些朋友做了最后的告别之后，离开了学校，来到学校外的一所出租屋里。这是之前和一个也准备二战的朋友商量好，要一起准备二战的。6月开始了二战复习，每天基本都在有条不紊地复习着，数学题、专业课，做的是真的头疼～不过这其中也有很多有意思的事情，我们几个人自己学炒菜做饭哈哈哈，还好我有一个家里开烧烤店的同学，他做的饭很奈斯，我也跟着他学了很多。我们是三个人合租，其中一个是和我一起准备二战的，另外一个是已经毕业很多年的老学长了，他也在附近工作，大哥人很好，帮了我们很多，他很快就要和他的对象结婚了，哈哈哈，希望能早点去他那吃席。这几个还发生了一件让人哭笑不得的事情，我们被误认为偷车贼，被拉到派出所喝茶了，真的是一件很难忘的经历了，不过好在警察叔叔人都很友善，在我们解释清楚之后就放我们离开了。不过有一说一，他们也挺辛苦的，记得那天晚上我们是凌晨离开的，他们还需要把事情的完整经过处理完毕才能下班回家。后来去了一趟洛阳，这是之前就和一个朋友约定好了的，可以看（古都洛阳）这篇文章。7月、8月每天除了数学题、专业课还是数学题和专业课，8月中旬的时候去了一趟北京，见了几个朋友，可以看（好久不见）这篇文章。9月因为一些事情，不得不提前结束出租屋里的学习，我回到了家里，然后开始了在家里的学习。10月参加了表哥的婚礼，看着他们举行婚礼的样子，说实话有些羡慕，哈哈哈不知道什么时候自己才能遇到适合自己的。10月中，我来到了家附近的自习室学习，好巧不巧的是有两个认识的小学同学也在这里学习，原来他家也在我们小区，没想到过了这么些年才知道，这也是一种缘分吧。在自习室的每天都很开心，早上早早的来，晚上晚晚的回去，而且这里也认识了新的朋友，学习之余也有一些小欢喜。终于考试那天还是来了，庆幸自己在考之前阳过一回，不然真不知道自己怎么坚持考完。距离考试还有一周的时候，我开始发烧，浑身无力酸痛，这个时候真的很无力，很担心这会影响到考试，那几天一点书都没看，甚至连起床的力气都没有，我清楚地记得，发烧的第一天在床上躺了一天，这一天只吃了一个鸡蛋，上了两个厕所，真的是很难受。幸运的是第三天就退烧了，然后马上开始继续复习功课，背诵记忆英语和政治。12月23日，来到了考试的地方，好在运气好一些，订到了房间，有一说一，那个房间真的是emmm难以想象，第一天晚上我翻过来覆过去的，不知道是床不舒服还是紧张，几乎整夜没睡，只有一丝印象大概是凌晨四点左右才睡了一会，很快6:30的闹钟响了，我急忙起床洗漱，最后看了一眼政治，出去吃了点东西进考场了。我们这里被划分为阴阳考场，运气比较好，考前阴了，被分到了阴考场。据一个在阳考场的朋友说，他们考场的老师穿着防护服监场，啊这，我不知道用什么词来吐槽了已经，真的，我们这些参加考试的孩子太不容易了。进了教室之后，才发现，30个位置空了十几个，到最后专业课考完的时候，总共就16个人坚持考完了。我记得很清楚，从第一场到最后一场，咳嗽基本没有停过，第一场的时候，旁边有个小姑娘也是一直在咳，可能是她太难受了，考完政治之后就没有来了，害，希望她身体没什么事。考完数学之后，又走了几个人，今年数学真的挺难的，不过，坚持下来的大家都是好样的！考完之后，我翻看网上的新闻帖子，发现有太多类似的情况了，好多好多人都是顶着烧去考试的，害，已经不知道怎么去形容了，考完之后我自己的心情也有些低落。一直在想，这半年多到底学了个啥，感觉考的什么也不是。政治一般，英语感觉还行，数学感觉很不好，专业课也一般般。考前复习的那么多很多都没有考到，也只能是自己的疏忽，还是复习的不够全面。考完回来之后，就开始有些焦虑了，一个是因为今年能上岸的几率不太大，另一个是不知道接下来要干什么了。也和一些朋友商量过，看看他们有什么想法，可大家都开始焦虑了，我实在是不愿意继续这样下去，时间太宝贵了。昨天晚上，我又了解到一个信息，就真的有些受到打击了。原来已经毕业的人是无法找实习的，实习只面对在校生。本来二战之前打算考哇之后找个实习积累一下项目经验，可是，现在这个是不行的，只能通过社招去找工作了。然后现在有几个问题：第一是直接进行社招找工作还是等待考研结果？第二出成绩之前这段时间要做些什么？昨天晚上和几个朋友商量了一下，得出了这样的结论：年后通过社招找工作，顺带等待成绩，如果能找到工作，先去工作。然后成绩下来之后如果可以进入复试，这期间再准备复试，是否能够通过复试。如果成绩不理想，那么继续工作就业。年前这段时间，尽可能的回忆以前的知识技能，重新学习新的技能，做几个项目，丰富一下简历。加油吧！这几天又把git操作重新回忆了一下，熟悉了一下基本操作，可以开始搞了，加油加油！！不管怎么说，生活还是要靠自己努力的，慢慢去经历，感受这个社会，体验过社会的打压之后才能更好的成长。加油！！希望2023年有好消息！！</li>
  <li>第一步购买节点通过这个网站进行注册，购买节点https://mojie.info/#/register?code=1H9faK7U也就是可以用来fq的流量。第二步下载fq软件https://ghproxy.com/https://github.com/ender-zhao/Clash-for-Windows_Chinese/releases/download/CFW-V0.19.21_CN/Clash.for.Windows.Setup.0.19.21.exe从该链接可以下载软件。然后在mojie网站主页面有个一键订阅，就可以将节点导入clash，然后更新订阅，开启代理即可fq。如果有问题还可以查看mojie这个网站的使用文档（网站左侧就可以看到），找到windows使用文档，然后根据所给信息一步一步操作。第三步淘宝购买奈飞账号推荐这个淘宝直接搜索奈飞账号就可以任意选择一个，购买，建议一个月一个月的买。第四步登录账号https://www.netflix.com/输入购买账号，即可登录观看。</li>
  <li>今天是很有意思的一天，我如约而至来到北京会见了两个好朋友A和B，还有A的对象C。这次地行程如果用一个词来形容的话，那就是神奇！上午的时候，我先是和好朋友B碰面，我俩相约一起去国家博物馆，有意思的地方来了，我反应了半天才看到她，感觉自己今天脑子完全不在线，好几次差点做错地铁走错路😂。她穿着一身黄裙，真的好好看，我这个笨脑袋都忘记夸人家了，该打该打！随后我二人来到博物馆进行观览，由于时间有限还有其他安排，我们也只是看了古代馆，有一说一值得表扬的地方就是所有古屋是按照时间线来展示的，每个朝代有着属于它的特色和标志性的物品。看到了不少很有回味的物件，像一些青铜镜，上面的花纹还有刻印的字迹都十分的生动，那上面的纹路真的很棒！还有一些古代农业用的灌溉器具、兵器、乐器，不由得想到了诗书礼乐，很大一种程度上感觉古人在这方面甚至比现代人做的还要好，研究的还要精通。期间帮她拍了几张照片，嗯自认为感觉还不错，希望她会喜欢，哈哈哈。她真的不像自己说的那样，她真的很上镜很上镜，以后有机会一起拍一组艺术写真照吧。不知不觉时间过去了大半，我们浅浅逛完了夏商周的一些古物就出来了，最后看到了四羊方尊，（大是真的大，纹路真的是太太太好看了，如果有小一点的纺织品的话，我一定买回来当水杯用）不过去没看到司母戊鼎，有点小可惜了。四十多分的地铁路程之后，我们抵达了目的地，京门老爆三，一家火锅店，运气还不错，有一个靠窗的位置，我们刚坐下不久，好友A带着她对象C（下文我称他大哥）就过来了，至此我四人终于齐聚！百闻不如一见，10分满分，我能给8分，长得确实很好看，而且看起来也很成熟，聊起来给人的感觉不仅成熟也很温和，真想不到这个A运气咋这好！有这么好一男朋友，我跟B我俩吐槽也就大哥能受了你这性格，哈哈哈。真好，我和B俩人看着他们两个，共同想说的就是“虽然不易，但真的真的希望你们能走到最后，要你俩都那啥了，我们就真不相信爱情了。”午餐过后我们找了一家星巴克点一些喝的东西，然后在那里休息了一会儿。我们聊聊这个聊那个还是很有趣的，歇息了一会之后，我就说我们真的要感谢一下我们四个共同的大学，真的真的真的，真的就是缘分吧，不然怎么可能有机会一起坐下来在这里聊天，还成为了好朋友。如果以后还有机会的话，我还是想和他们三个人一起坐在一家餐厅或者一家小酒馆，再次说说笑笑。有个小插曲，哈哈哈，我们四个随后在这个商场里逛了逛，最后来到了一家玩具店？可以买到毛绒玩具的地方，这时候我觉得自己还算有点脑子，帮B选了一只小老虎😂，手感非常之不错。本来本来他们三个我是都有买礼物的，只是浙江那边疫情了，快递发不过来了还是临时被通知的，这就有点尴尬了，所以大哥你俩别怪我。晚上简单喝了点小米粥就踏上了归程。好家伙，直接来了一波生死时速😂7:30买票，7:34上电梯，7:36找到检票口，7:38坐下，7:39发车！老天开眼，今天运气变好了，我没来得及看车站的电子屏幕，所以一开始根本是乱找检票口，但是但是，我刚上电梯往前面走了几个站口就看到了我那趟车次的检票口。最终坐上了回去的高铁！奈斯！！最后的最后，说一句最俗的话，“希望我们平安喜乐。”</li>
  <li>这几天睡觉做了一个很奇怪的梦，以前做个梦，基本第二天就忘记了，可这次做的梦不仅仅真实，而且为什么会有种要发生的感觉呢？我不明白这种感觉，于是把这个不长的故事记录下来……当我醒来的时候，深处一片一望无际的旷野之中，周围全是生长着一种不知名的金黄色谷物，闻起来还有点香，可是我实在不敢去试吃一下。我分不清这是梦境还是现实，因为它是如此的真实，我甚至能感觉到自己的内心有些惊喜和躁动。有种中二之魂觉醒的感觉，可现实是根本没有魔法和什么系统之类的设定，有的也只有这片旷野以及孤零零的一个人——我。我开始漫无目的的朝着一个方向前行，为了避免自己在走圆，我撕破自己的衣角，朝着衣角被风吹向的方向前进。但我也没有十足的把握风向不会改变，这也只是暂时的。也不知是过了多久，在我能看到的水平线之上，出现了一座建筑，建筑很奇怪，地基的外围是被一圈水给环起来的，这个奇怪的房子只有一扇门，没有窗户，整体形状是方形的身体和拱形的房顶，而且房子的墙上画满了奇怪的花纹，像是在祈祷着什么。好吧，确实是有些口渴和累了，我也需要一个遮风挡雨的地方，于是我先是半漂半游的到了门口，好在水不深，门口很大，真的很大，因为它没有门，只有一个光秃秃的口，里面很黑很黑，似乎人要被黑暗吞噬一般。片刻之后，我摸着黑看到了少许光亮，我慢慢地移动着批发商的身子，渐渐的渐渐的，白光取代了黑暗……..本来深处荒野之上就已经很奇怪了，可是里面的景象却是让我更加的激动万分。明明在外面看到这个房子是由屋顶的，可进来之后却是极其的明亮，抬头看到的不是黑漆漆的屋顶，而是金褐色带点蔼红的天空，不禁感叹道这屋顶是什么材料做的。映入眼帘的首先是巨大的落地窗，巨型的吊灯，圆形凸起的灯的周围似乎都镶嵌着不菲的钻石，还有巨型的地毯铺满整个房间，这俨然是一副宴会厅的景象，墙上挂满了说不上名的画像，只是奇怪这些画像我没有一丝一毫的印象，就好像他们从未出现过。为什么一片荒野之上会有这样一座格格不入的建筑呢，我又为何来到了这里呢，这个宴会厅似乎在等待着什么人的到来，我不知道自己是否能够被欢迎，但我的本能告诉我自己，这些不是为我准备的，我是个外来人员，甚至是一个不速之客。还有一个问题，来到这里的真的是“人”吗，这些未免也太过巨大了，我不得而知……我开始感觉到不安，我往房子的深处走去，终于横跨了这个奢华的“宴会厅”，明亮之后又是黑暗，而黑暗之后又是明亮，只是这次的明亮却不是让人感觉到舒适的明亮，而是一片寒冷的死寂，让人不寒而颤。我又来到了一片草地之上，但我还是在这个房子里面，这个房子的内部构造究竟是怎样的，从外面看的时候，并不觉得里面有很大的空间，因为这个房子怎么看都不像有着很大空间的房子，相反它很小，可里面却是……可随后却经历了我始终难以忘记的画面，草地的不远处有一处看起来像是用木桩围起来的土地，我向着那里走去，看看是否可以找到些什么线索，可我发现，这块地，除了没有草，还有些黑褐色的痕迹几乎和普通的土地没有差别。紧接着一种刺耳的声音，像是一种猛兽的嚎叫，但却比那又尖锐许多，不仅整个身体在颤抖，而且神经似乎也被这声音影响，远处一个黑影在急速地向我前进，不一会它就出现在我的面前，深渊巨口，硕大的獠牙，巨大的蟒蛇霎时间出现在我的眼前，我懂了，这是它的食堂，那些黑褐色的痕迹是凝固后的血，看来我就要成为这畜生的美餐了……..我几乎是带着哭丧还有疲惫的身体向它身后跑去，可它的速度太快，还没等一个照面，它那巨大的尾巴直接扫在我的身体上，我也在天空之上滑过一道抛物线，但却是狠狠地摔在了地上，口吐鲜血，身体各处，都在通过肌肉向我的大脑传递着撕心裂肺的疼痛，这畜生就那样一副居高临下的眼神看着我——“它的美餐”，可是我已经没有力气动了，那畜生缓慢的移动着身体，张开它那深渊般的巨口，我能明显的感觉到自己的生命力在不断地流失，眼神也逐渐涣散…….但弥留之际，在我即将闭上双眼之时，恍惚中看到了远处有一个人影，他亦或是她亦或是它在盯着我……以上就是梦的大致的内容，对我来说emm真的很奇妙，我在清醒的时候还在想，那个远处在盯着我的人是会救我呢，还是就这样看着我被大蛇吃掉呢……</li>
  <li>首先看顺序表的定义：一组地址连续的存储单元，顺序存储线性表中的数据单元，使得逻辑上两个相邻的元素在物理位置上也相邻。这里就有一个疑问了，数组和顺序表又有什么关系？顺序表在计算机内以数组形式保存。线性表是从逻辑角度来看待的，它除了首和尾，其他每一个元素都有一个前驱和后继元素。数组则是从物理贮存角度来看待的，不仅顺序表可以用数组来存储，队列和栈也可以。顺序表和数组都是数据结构，只是描述角度不同，不过数组是一个更大的概念。接下来，我们看代码：typedefstruct{intdata[MaxSize];intlength;}SqList;//初始化线性表voidInitList(SqList&amp;L){for(inti=0;i&lt;MaxSize;++i){L.data[i]=0;}L.length=0;//空表}因为我要执行插入和删除操作，所以我先定义了一个空表，但随后我执行插入和删除操作的时候，却一直失败，我反复检查插入和删除代码，发现并无问题。经过一番思考查找之后，我发现问题出在L.length=0上面，是的，我建立了一个空表，我在表里面添加数据的时候，并没有对L.length进行++操作。所以使得我的输出结果始终得不到我想要的。好在，最后解决！附上完整代码：#include"stdio.h"#include"stdlib.h"#defineMaxSize10typedefstruct{intdata[MaxSize];intlength;}SqList;//初始化线性表voidInitList(SqList&amp;L){for(inti=0;i&lt;MaxSize;++i){L.data[i]=0;}L.length=9;//这里初始化应该为0才对，由于为了数据测试方便，因此设为9}//插入操作boolListInsert(SqList&amp;L,inti,inte){if(i&lt;1||i&gt;L.length){//判断i的范围是否正确returnfalse;}if(L.length&gt;=MaxSize){//判断空间是否已满returnfalse;}for(intj=L.length;j&gt;=i;j--){//讲第i个元素及之后元素后移L.data[j]=L.data[j-1];}L.data[i-1]=e;//讲元素e赋给第i-1（数组下标从0开始）个位置L.length++;returntrue;}//删除操作boolListDelete(SqList&amp;L,inti,int&amp;e){if(i&lt;1||i&gt;L.length){returnfalse;}e=L.data[i-1];//将被删除元素赋给efor(intj=i;j&lt;L.length;j++){L.data[j-1]=L.data[j];//将第i个位置之后的元素前移}L.length--;returntrue;}intmain(){printf("helloworld\n");SqListL;//初始化InitList(L);for(inti=0;i&lt;MaxSize;i++){printf("data[%d]=%d\n",i,L.data[i]);}for(inti=0;i&lt;8;++i){L.data[i]=i;//L.length++;printf("data[%d]=%d\n",i,L.data[i]);}printf("******************************\n");printf("L.leng=%d\n",L.length);//插入boolflagIn=ListInsert(L,2,33);printf("flag:%d\n",flagIn);for(inti=0;i&lt;MaxSize;i++){printf("data[%d]=%d\n",i,L.data[i]);}printf("******************************\n");//删除inte=-1;boolflagDel=ListDelete(L,2,e);printf("falg:%d\n",flagDel);for(inti=0;i&lt;MaxSize;i++){printf("data[%d]=%d\n",i,L.data[i]);}printf("删除的元素是%d\n",e);printf("******************************\n");return0;}</li>
  <li>前几日，偶然看到「意难平」三个字，它是在一个小说推荐视频中反复出现的弹幕，我不禁感慨道，截止到现在为止，又有多少能被我认为意难平的往事呢。视频中排在首位的是一本叫做《我的26岁房客》的书，看到书名的第一眼，很难联想到它怎会跟「意难平」三个字产生关系，根据我的臆想，顶多是男女之间的爱恨情仇，于是我饶有兴趣地找到了这本书，静静地读了下去……读完这本书花了我两天左右的时间，作者的文笔很细腻，尤其是主角之间的对话还有对肢体语言的描写，能够让人联想出一幅幅画面，每个主人公有着每个自己的故事，在名为苏州的这所城市，不断地交织产生联系，或者兴奋、又或者悲伤、又或者无奈、又或者读者感受到「意难平」。人生世事无常，刚刚读完这本书的我甚至还有些不太能体会作者想要表达的内涵，只是在看到作者用「还活着」做为这本书的后记的时候，我突然意识到小说中主人公们遇到的种种不幸，虽然一段时间中让他们感到伤心、难过、烦恼….可最重要的他们真真切切的还活着！借用书中的一句话就是：我们怎么可以奢侈的浪费掉活着的幸运......是啊，再大的不幸与不公跟生命比起来，简直如同秋毫！我想这应该就是作者想要表达的意图吧！小说的主人公叫昭阳，谐音就是朝阳，女主人公叫米彩，象征的应该是彩虹，雨过天晴现彩虹🌈，小说的开篇也就造就了这本书的解决注定是个happyend。只是每当读到书中角色们的一个个又一个个的故事的时候，很难一直让自己处于一个很理性的情绪。「意难平」无非逃不过一个字——情，即是亲情、爱情、友情。而这其中又最为纠结、缠绵的就是爱情。我为什么用到了缠绵两个字，因为在我看来一段关系，不管是否有结果，始终是埋下了种子，生了根….小说只是通过了几个年轻人（额，26、27岁算年轻人吧也）的故事就将这三种情表达的不得不让人说好！读完之后，内心所想对于一些角色只有无限的惋惜，可惜没有如果，真正的「意难平」在我看来只有亲身经历过的人才最能读懂，最能感知。我给这篇文章起名为天空之城，并不是我想的，而是书中的主人公昭阳想的。在他的认知中，一直有一座幻想的天空城，它可以被建立、被装饰、被完善、也可以崩塌，完完全全取决于他的生活，也许他今天是一个成功人士，也许今天他是一个落魄的孤独的人。这本书真的很有意思，一个意外，昭阳（男主）在最落魄、最不堪、最无耻的时候邂逅了米彩（女主），两个人的生活天差地别，却因为房东和房客的归属产生了联系。生活不缺少意外，也不缺少邂逅，只是有时候这种意外不太那么容易发生而已。我不想太多的去探讨这本书讲的是什么，但是它放佛有肿魔力吸引着我，促使我去感知主人公们的故事，共情他们的心情，虽然没有绝对的共情，可当带入到那种情绪当中去，我相信一个正常人是不会没有想法和动作的。也许我们每个人都有自己一个小小天空城，这里面也许会住满了你的情绪，你高兴它就牢固，你不高兴它就支离破碎，可又有什么关系，至少我们还活着，不是吗，这不就是最大的幸运。</li>
  <li>古都洛阳2022年6月14号凌晨，廊坊的行程码终于不带星星了，终于，这座城市变成低风险地区了，这也就意味着，之前计划好久洛阳的旅途可以开始了。出发前一天的傍晚6月15号上午十点多我坐上了去往洛阳的列车，时隔多年，第一次坐这么久的火车，属实是有些不适应。旅途开始了。饿，这着实有些令人想不到，火车一经发动手机信号就一直很差，导致我中间和朋友联系的时候时常没有信号和网络。好在，我带了一本书《达芬奇密码》，路上还可以解解闷，手机就变成了无脑的拍照机器了。这趟火车挺有意思的，坐在我对面的大叔是要坐到这趟列车的终点站，昆明。好家伙，这得坐三天两夜了！大叔说，来的时候坐的飞机只有几百块，现在回家的话坐飞机得一千多了，坐火车能省好几百。我心里想想，没准这也会是我以后的常态，为了剩下几百块钱选择不那么舒服的绿皮火车。到达保定站的时候，又上来一位大叔，这回是坐我旁边，我的位置靠窗，起初没太注意，只是我在看书或者打瞌睡的时候，大叔嘴里一直念叨着什么，而且声音还很大。每当乘务员推着卖吃的东西的小推车过来的时候，他会买一瓶酒，emm咱就说味道真的很大。我也听不懂他在说什么，总之挺难沟通的。倒是下一站，上来一对夫妇，带着个小孩，很可爱，他们要去贵阳，听他们说要回女方的家，好长时间没回去过了，带着孩子回去一次。有趣的事情来了，坐我旁边的大叔似乎很喜欢这个小孩子，一直在逗他，虽然我并不觉得小孩有被他逗到。后来听他说，是因为这个小孩跟他家里的孩子很像，孙子或者孙女吧，大叔看着他就想到了自己家的孩子。因为这个小孩子的出现，给了这些互不认识的陌生人一个可以沟通的桥梁。我并未介入他们的对话，只是作为一个暂时的同路人倾听着他们的对话……好长好长好长时间后（十多小时）终于抵达了目的地—洛阳关林站。来这里之后的第一顿，芜湖，虽然不是很饿了，但是这些小吃都十分的美味，而且很有特色，虽然我想不起来叫什么了哈哈哈哈，最喜欢的还是海碧（洛阳当地自己生产的饮料），海碧yyds！！她把我送回酒店后，我洗洗睡了过去。然后我们浅浅逛了一下博物馆，嗯“浅浅”，脚会痛的那种。不过有一说一，有很多很好看的文物，真的很吸引。最喜欢的是白玉杯，很纯粹的玉透着淡淡的光泽，我想象不出古人是如何烧制出来的，如果仅仅是烧制一个玉杯那很容易，可玉杯上面的纹路又是运用了什么样的工艺……晚上去了十字街，好家伙是真的好家伙。人好多巨多！路两边都是卖各种吃的，emmm叫不上名字了已经哈哈哈哈哈，不过都超级好吃！第三天下午去了商场、浅浅逛了一下，我买了件无袖白色上衣，她买了两个吊带，蛮适合她的。还有憨批的二柱子哈哈哈哈晚上去了洛邑古城，芜湖，小巷的感觉十分棒，还喝了很好喝的丸子汤！到这里就能看到巨多的穿汉服的小姐姐。随着天色渐渐暗下来，古城的黄晕色光缓缓亮起，这座古城似乎又活了过来，一呼一吸，一呼一吸，两个感受着这座古城的脉动。晚上送她回家后我骑车回酒店，再次好家伙，迷路了😂，真的不怪我天太黑了，废了好大劲才找到另外一条回酒店的路，回去跟她说我迷路了，给她笑死。第四天，去了白马寺，跟随她的步伐一起祭拜了一下，求个平安，亲人朋友们都好好的。不过那个止语茶舍很有意思，如同字面，大家进去只是喝茶歇息，人们也只是轻轻小声交流，生怕惊扰了这寺庙中的禅僧。走之前终于看到了寺猫，它好慵懒哈哈哈哈哈，轻轻让我摸了摸就跑去晒太阳的，不过它身上的味道好香，可能是因为这所寺庙的缘故吧。晚上来到了应天门，本来还想去什么什么公园，又给忘了，这个地名确实不太好记哈。我们浅浅逛了一下，然后去吃了点东西，送她回家后我回酒店休息，小眯一会后坐上了凌晨三点的火车。也不知道写了多少字，写了一个小时多点，外面的天也慢慢亮了，刚刚还看到了浅红紫颜色的朝霞，这趟旅途也就到此结束了。下次再来不知又是什么时候了，总之长路漫漫，我也愿做一个苦修去往下一个站点。咱就说长途旅行好累…….</li>
  <li>标题这里是h1这里是h2这里是h3这里是h4这里是h5这里是h6#这里是h1##这里是h2###这里是h3####这里是h4#####这里是h5######这里是h6段落段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落超链接TMaizeBlog[TMaizeBlog](http://blog.tmaize.net)引用这里是引用&gt;这里是引用常见字体样式斜体粗体删除线_斜体_**粗体**~~删除线~~列表无序列表1-1缩进2空格缩进2空格缩进2空格无序列表1-2无序列表1-3有序列表1-1缩进3空格缩进3空格缩进3空格有序列表1-2有序列表1-3-无序列表1-1缩进2空格-缩进2空格-缩进2空格-无序列表1-2-无序列表1-31.有序列表1-1缩进3空格1.缩进3空格2.缩进3空格2.有序列表1-23.有序列表1-3分割线---图片muku1miku![line](http://xx.com/xx.jpg)代码行这是一段文字rm-rf/*这是一段文字这是一段文字`rm-rf/*`这是一段文字代码块blog.encodeHtml=function(html){varo=document.createElement('div')o.innerText=htmlvartemp=o.innerHTMLo=nullreturntemp}```javascriptblog.encodeHtml=function(html){varo=document.createElement('div')o.innerText=htmlvartemp=o.innerHTMLo=nullreturntemp}```表格测试TablesAreCoolcol3isright-aligned$1600col2iscentered$12zebrastripesareneat$1|Tables|Are|Cool||-------------|:-----------:|-----:||col3is|right-aligned|\$1600||col2is|centered|\$12||zebrastripes|areneat|\$1|数学公式需要在配置中设置extMath:true开启#行内\(\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}\)$\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}$#段落\[\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}\]$$\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}$$Loremipsumdolorsit\(\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}\)ametconsecteturadipisicingelit.Loremipsumdolorsit$\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}$ametconsecteturadipisicingelit.Loremipsumdolorsitametconsecteturadipisicingelit.Adautassumendadistinctioevenietquos,saepenonquasiminusfacereisteodit!Accusamuseosoptio,arecusandaenequealiquamprovidentillum?\[\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}\]Loremipsumdolorsitametconsecteturadipisicingelit.Adautassumendadistinctioevenietquos,saepenonquasiminusfacereisteodit!Accusamuseosoptio,arecusandaenequealiquamprovidentillum?$$\int_0^\infty\frac{x^3}{e^x-1}\,dx=\frac{\pi^4}{15}$$Loremipsumdolorsitametconsecteturadipisicingelit.Adautassumendadistinctioevenietquos,saepenonquasiminusfacereisteodit!Accusamuseosoptio,arecusandaenequealiquamprovidentillum?插入html&lt;divid="htmldemo"&gt;&lt;/div&gt;&lt;style&gt;#htmldemo{height:30px;width:30px;background-color:#00aa9a;animation-name:moveX;animation-duration:1s;animation-timing-function:linear;animation-iteration-count:infinite;animation-direction:alternate;animation-fill-mode:both;}@keyframesmoveX{0%{transform:translateX(0px);}100%{transform:translateX(100px);}}&lt;/style&gt;插入iframe&lt;!--属性什么的不要错了，最好用双引号括住--&gt;&lt;!--网易云的iframe需要做些调整，调整如下--&gt;&lt;iframesrc="//music.163.com/outchain/player?type=2&amp;id=28445796&amp;auto=0&amp;height=66"frameborder="0"width="100%"height="86px"&gt;&lt;/iframe&gt;</li>
</ul>