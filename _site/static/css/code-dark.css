html.dark .highlight,
html.dark pre.highlight {
  /* background: #282c34; */
  background: #161b22;
  color: #abb2bf;
}
html.dark .highlight pre {
  /* background: #282c34; */
  background: #161b22;
}
html.dark .highlight .hll {
  background: #282c34;
}
html.dark .highlight .c {
  color: #5c6370;
  font-style: italic;
}
html.dark .highlight .err {
  color: #960050;
  background-color: #1e0010;
}
html.dark .highlight .k {
  color: #c678dd;
}
html.dark .highlight .l {
  color: #98c379;
}
html.dark .highlight .n {
  color: #abb2bf;
}
html.dark .highlight .o {
  color: #abb2bf;
}
html.dark .highlight .p {
  color: #abb2bf;
}
html.dark .highlight .cm {
  color: #5c6370;
  font-style: italic;
}
html.dark .highlight .cp {
  color: #5c6370;
  font-style: italic;
}
html.dark .highlight .c1 {
  color: #5c6370;
  font-style: italic;
}
html.dark .highlight .cs {
  color: #5c6370;
  font-style: italic;
}
html.dark .highlight .ge {
  font-style: italic;
}
html.dark .highlight .gs {
  font-weight: 700;
}
html.dark .highlight .kc {
  color: #c678dd;
}
html.dark .highlight .kd {
  color: #c678dd;
}
html.dark .highlight .kn {
  color: #c678dd;
}
html.dark .highlight .kp {
  color: #c678dd;
}
html.dark .highlight .kr {
  color: #c678dd;
}
html.dark .highlight .kt {
  color: #c678dd;
}
html.dark .highlight .ld {
  color: #98c379;
}
html.dark .highlight .m {
  color: #d19a66;
}
html.dark .highlight .s {
  color: #98c379;
}
html.dark .highlight .na {
  color: #d19a66;
}
html.dark .highlight .nb {
  color: #e5c07b;
}
html.dark .highlight .nc {
  color: #e5c07b;
}
html.dark .highlight .no {
  color: #e5c07b;
}
html.dark .highlight .nd {
  color: #e5c07b;
}
html.dark .highlight .ni {
  color: #e5c07b;
}
html.dark .highlight .ne {
  color: #e5c07b;
}
html.dark .highlight .nf {
  color: #abb2bf;
}
html.dark .highlight .nl {
  color: #e5c07b;
}
html.dark .highlight .nn {
  color: #abb2bf;
}
html.dark .highlight .nx {
  color: #abb2bf;
}
html.dark .highlight .py {
  color: #e5c07b;
}
html.dark .highlight .nt {
  color: #e06c75;
}
html.dark .highlight .nv {
  color: #e5c07b;
}
html.dark .highlight .ow {
  font-weight: 700;
}
html.dark .highlight .w {
  color: #f8f8f2;
}
html.dark .highlight .mf {
  color: #d19a66;
}
html.dark .highlight .mh {
  color: #d19a66;
}
html.dark .highlight .mi {
  color: #d19a66;
}
html.dark .highlight .mo {
  color: #d19a66;
}
html.dark .highlight .sb {
  color: #98c379;
}
html.dark .highlight .sc {
  color: #98c379;
}
html.dark .highlight .sd {
  color: #98c379;
}
html.dark .highlight .s2 {
  color: #98c379;
}
html.dark .highlight .se {
  color: #98c379;
}
html.dark .highlight .sh {
  color: #98c379;
}
html.dark .highlight .si {
  color: #98c379;
}
html.dark .highlight .sx {
  color: #98c379;
}
html.dark .highlight .sr {
  color: #56b6c2;
}
html.dark .highlight .s1 {
  color: #98c379;
}
html.dark .highlight .ss {
  color: #56b6c2;
}
html.dark .highlight .bp {
  color: #e5c07b;
}
html.dark .highlight .vc {
  color: #e5c07b;
}
html.dark .highlight .vg {
  color: #e5c07b;
}
html.dark .highlight .vi {
  color: #e06c75;
}
html.dark .highlight .il {
  color: #d19a66;
}
html.dark .highlight .gu {
  color: #75715e;
}
html.dark .highlight .gd {
  color: #f92672;
}
html.dark .highlight .gi {
  color: #a6e22e;
}
