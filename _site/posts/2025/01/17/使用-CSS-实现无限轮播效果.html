<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>使用 CSS 实现无限轮播效果-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="使用 CSS 实现无限轮播效果"/>
  <meta name="keywords" content="slince,前端,CSS"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="使用 CSS 实现无限轮播效果">使用 CSS 实现无限轮播效果</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2025-01-17
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#前端" class="hover-underline">前端</a>
      
        <a href="/pages/categories.html#CSS" class="hover-underline">CSS</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>今天和大家分享一个使用纯 CSS 实现的无限轮播效果。这个轮播效果不需要任何 JavaScript，完全依靠 CSS 动画来实现，而且支持鼠标悬停暂停。</p>

<h2 id="效果展示">效果展示</h2>

<iframe src="/demos/infinite-carousel.html" width="100%" height="300px" frameborder="0"></iframe>

<h2 id="实现原理">实现原理</h2>

<p>实现这个效果的核心在于：</p>

<ol>
  <li>创建两组完全相同的内容</li>
  <li>使用 CSS animation 让内容持续向左滚动</li>
  <li>当第一组内容完全滚出视野时，第二组内容刚好补上，造成无缝衔接的效果</li>
</ol>

<h2 id="代码实现">代码实现</h2>

<p>首先是 HTML 结构：</p>

<div class="language-html highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"carousel"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"group"</span><span class="nt">&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>A<span class="nt">&lt;/div&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>B<span class="nt">&lt;/div&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>C<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;/div&gt;</span>
  
  <span class="c">&lt;!-- 复制一组相同的内容 --&gt;</span>
  <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"group"</span> <span class="na">aria-hidden=</span><span class="s">"true"</span><span class="nt">&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>A<span class="nt">&lt;/div&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>B<span class="nt">&lt;/div&gt;</span>
    <span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"card"</span><span class="nt">&gt;</span>C<span class="nt">&lt;/div&gt;</span>
  <span class="nt">&lt;/div&gt;</span>
<span class="nt">&lt;/div&gt;</span>
</code></pre></div></div>

<p>CSS 样式：</p>

<div class="language-css highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nc">.carousel</span> <span class="p">{</span>
  <span class="nl">margin</span><span class="p">:</span> <span class="m">0</span> <span class="nb">auto</span><span class="p">;</span>
  <span class="nl">padding</span><span class="p">:</span> <span class="m">20px</span> <span class="m">0</span><span class="p">;</span>
  <span class="nl">max-width</span><span class="p">:</span> <span class="m">700px</span><span class="p">;</span>
  <span class="nl">overflow</span><span class="p">:</span> <span class="nb">hidden</span><span class="p">;</span>
  <span class="nl">display</span><span class="p">:</span> <span class="n">flex</span><span class="p">;</span>
  <span class="err">&gt;</span> <span class="err">*</span> <span class="err">{</span>
    <span class="nl">flex</span><span class="p">:</span> <span class="m">0</span> <span class="m">0</span> <span class="m">100%</span><span class="p">;</span>
  <span class="p">}</span>

  <span class="o">&amp;</span><span class="nd">:hover</span> <span class="nc">.group</span> <span class="p">{</span>
    <span class="nl">animation-play-state</span><span class="p">:</span> <span class="n">paused</span><span class="p">;</span>
    <span class="nl">cursor</span><span class="p">:</span> <span class="nb">pointer</span><span class="p">;</span>
  <span class="p">}</span>
<span class="err">}</span>

<span class="nc">.card</span> <span class="p">{</span>
  <span class="nl">width</span><span class="p">:</span> <span class="m">100%</span><span class="p">;</span>
  <span class="nl">color</span><span class="p">:</span> <span class="m">#000</span><span class="p">;</span>
  <span class="nl">border-radius</span><span class="p">:</span> <span class="m">24px</span><span class="p">;</span>
  <span class="nl">box-shadow</span><span class="p">:</span> <span class="n">rgba</span><span class="p">(</span><span class="m">0</span><span class="p">,</span> <span class="m">0</span><span class="p">,</span> <span class="m">0</span><span class="p">,</span> <span class="m">10%</span><span class="p">)</span> <span class="m">5px</span> <span class="m">5px</span> <span class="m">20px</span> <span class="m">0</span><span class="p">;</span>
  <span class="nl">padding</span><span class="p">:</span> <span class="m">20px</span><span class="p">;</span>
  <span class="nl">font-size</span><span class="p">:</span> <span class="nb">xx-large</span><span class="p">;</span>
  <span class="nl">justify-content</span><span class="p">:</span> <span class="nb">center</span><span class="p">;</span>
  <span class="nl">align-items</span><span class="p">:</span> <span class="nb">center</span><span class="p">;</span>
  <span class="nl">min-height</span><span class="p">:</span> <span class="m">200px</span><span class="p">;</span>

  <span class="err">&amp;:nth-child(1)</span> <span class="err">{</span> <span class="nl">background</span><span class="p">:</span> <span class="m">#7958ff</span><span class="p">;</span> <span class="p">}</span>
  <span class="o">&amp;</span><span class="nd">:nth-child</span><span class="o">(</span><span class="err">2</span><span class="o">)</span> <span class="p">{</span> <span class="nl">background</span><span class="p">:</span> <span class="m">#7bb265</span><span class="p">;</span> <span class="p">}</span>
  <span class="o">&amp;</span><span class="nd">:nth-child</span><span class="o">(</span><span class="err">3</span><span class="o">)</span> <span class="p">{</span> <span class="nl">background</span><span class="p">:</span> <span class="m">#d29b9b</span><span class="p">;</span> <span class="p">}</span>
<span class="err">}</span>

<span class="nc">.group</span> <span class="p">{</span>
  <span class="nl">display</span><span class="p">:</span> <span class="n">flex</span><span class="p">;</span>
  <span class="py">gap</span><span class="p">:</span> <span class="m">20px</span><span class="p">;</span>
  <span class="nl">padding-right</span><span class="p">:</span> <span class="m">20px</span><span class="p">;</span>
  <span class="py">will-change</span><span class="p">:</span> <span class="n">transform</span><span class="p">;</span>
  <span class="nl">animation</span><span class="p">:</span> <span class="n">scrolling</span> <span class="m">10s</span> <span class="n">linear</span> <span class="n">infinite</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">@keyframes</span> <span class="n">scrolling</span> <span class="p">{</span>
  <span class="err">0</span><span class="o">%</span> <span class="p">{</span>
    <span class="nl">transform</span><span class="p">:</span> <span class="n">translateX</span><span class="p">(</span><span class="m">0</span><span class="p">);</span>
  <span class="p">}</span>
  <span class="err">100</span><span class="o">%</span> <span class="p">{</span>
    <span class="nl">transform</span><span class="p">:</span> <span class="n">translateX</span><span class="p">(</span><span class="m">-100%</span><span class="p">);</span>
  <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<h2 id="关键点解析">关键点解析</h2>

<ol>
  <li><strong>容器设置</strong>
    <ul>
      <li>使用 <code class="language-plaintext highlighter-rouge">overflow: hidden</code> 隐藏超出部分</li>
      <li>使用 <code class="language-plaintext highlighter-rouge">display: flex</code> 让两组内容水平排列</li>
    </ul>
  </li>
  <li><strong>动画实现</strong>
    <ul>
      <li>使用 <code class="language-plaintext highlighter-rouge">@keyframes</code> 定义动画，从 0% 位置移动到 -100% 位置</li>
      <li>设置 <code class="language-plaintext highlighter-rouge">animation: scrolling 10s linear infinite</code> 使动画无限循环</li>
      <li><code class="language-plaintext highlighter-rouge">linear</code> 确保动画速度均匀</li>
    </ul>
  </li>
  <li><strong>性能优化</strong>
    <ul>
      <li>使用 <code class="language-plaintext highlighter-rouge">will-change: transform</code> 提前告知浏览器即将发生变换</li>
      <li>使用 <code class="language-plaintext highlighter-rouge">transform</code> 而不是改变 <code class="language-plaintext highlighter-rouge">left</code> 或 <code class="language-plaintext highlighter-rouge">margin</code> 属性，可以获得更好的性能</li>
    </ul>
  </li>
  <li><strong>交互体验</strong>
    <ul>
      <li>当鼠标悬停时，设置 <code class="language-plaintext highlighter-rouge">animation-play-state: paused</code> 暂停动画</li>
      <li>添加 <code class="language-plaintext highlighter-rouge">cursor: pointer</code> 提示用户可交互</li>
    </ul>
  </li>
  <li><strong>无障碍优化</strong>
    <ul>
      <li>为第二组重复内容添加 <code class="language-plaintext highlighter-rouge">aria-hidden="true"</code>，避免屏幕阅读器重复读取</li>
    </ul>
  </li>
</ol>

<h2 id="使用场景">使用场景</h2>

<p>这种无限轮播效果适用于：</p>
<ul>
  <li>展示产品特性</li>
  <li>显示用户评价</li>
  <li>展示合作伙伴 logo</li>
  <li>新闻标题滚动等</li>
</ul>

<h2 id="注意事项">注意事项</h2>

<ol>
  <li>动画时长需要根据内容长度和期望的滚动速度来调整</li>
  <li>内容数量不宜过多，否则会影响性能</li>
  <li>在移动端需要注意性能问题</li>
  <li>建议添加媒体查询，在小屏幕设备上调整合适的显示方式</li>
</ol>

<h2 id="总结">总结</h2>

<p>这个纯 CSS 实现的无限轮播效果，代码简洁，性能良好，而且不需要依赖任何 JavaScript 库。通过合理运用 CSS 动画和变换，我们可以实现很多有趣的效果。希望这个例子能给大家一些启发！</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>