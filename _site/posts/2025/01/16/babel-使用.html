<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>babel 使用-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="babel 使用"/>
  <meta name="keywords" content="slince,教程"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="babel 使用">babel 使用</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2025-01-16
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#教程" class="hover-underline">教程</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <h1 id="什么是-babel">什么是 Babel</h1>

<p>Babel 是一个 JavaScript 编译器,提供了JavaScript的编译过程，能够将源代码转换为目标代码。</p>

<p>AST -&gt; Transform -&gt; Generate</p>

<p>官网 https://babeljs.io/</p>

<p>查看AST https://astexplorer.net/</p>

<p>Babel所有的包 https://babeljs.io/docs/babel-traverse</p>

<h2 id="核心功能">核心功能</h2>
<ol>
  <li>语法转换：将新版本的 JavaScript 语法转换为旧版本的语法</li>
  <li>Polyfill：通过引入额外的代码，使新功能在旧浏览器中可用</li>
  <li>JSX: 将JSX语法转换成普通的JavaScript语法</li>
  <li>插件: 为Babel提供自定义功能</li>
</ol>

<h1 id="babel-插件开发示例">Babel 插件开发示例</h1>

<p>本<a href="https://github.com/slince-zero/babel-exercise">项目</a>包含了多个 Babel 使用示例，包括自定义插件开发、ES6 转 ES5、以及 JSX 转换。通过这些示例，展示了 Babel 在代码转换方面的强大功能。</p>

<h2 id="项目结构">项目结构</h2>

<ul>
  <li><code class="language-plaintext highlighter-rouge">index-plugin.js</code>: 箭头函数转换插件实现文件</li>
  <li><code class="language-plaintext highlighter-rouge">test-plugin.js</code>: 包含待转换箭头函数的测试文件</li>
  <li><code class="language-plaintext highlighter-rouge">index.js</code>: ES6 转 ES5 示例文件</li>
  <li><code class="language-plaintext highlighter-rouge">index-jsx.js</code>: JSX 转换示例文件</li>
  <li><code class="language-plaintext highlighter-rouge">test.js</code>: ES6 测试代码</li>
  <li><code class="language-plaintext highlighter-rouge">test.jsx</code>: JSX 测试代码</li>
</ul>

<h2 id="功能示例">功能示例</h2>

<h3 id="1-es6-转-es5indexjs">1. ES6 转 ES5（index.js）</h3>

<p>使用 <code class="language-plaintext highlighter-rouge">@babel/preset-env</code> 将 ES6+ 代码转换为 ES5，支持按需 polyfill：</p>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">import</span> <span class="nx">Babel</span> <span class="k">from</span> <span class="dl">'</span><span class="s1">@babel/core</span><span class="dl">'</span>
<span class="k">import</span> <span class="nx">presetEnv</span> <span class="k">from</span> <span class="dl">'</span><span class="s1">@babel/preset-env</span><span class="dl">'</span>

<span class="kd">const</span> <span class="nx">res</span> <span class="o">=</span> <span class="nx">Babel</span><span class="p">.</span><span class="nf">transform</span><span class="p">(</span><span class="nx">file</span><span class="p">,</span> <span class="p">{</span>
  <span class="na">presets</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">[</span>
      <span class="nx">presetEnv</span><span class="p">,</span>
      <span class="p">{</span>
        <span class="c1">// usage: 按需引入 polyfill</span>
        <span class="c1">// entry: 手动引入全部 polyfill</span>
        <span class="na">useBuiltIns</span><span class="p">:</span> <span class="dl">'</span><span class="s1">usage</span><span class="dl">'</span><span class="p">,</span>
        <span class="c1">// 使用 core-js 3.x 版本进行 polyfill</span>
        <span class="na">corejs</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span>
      <span class="p">},</span>
    <span class="p">],</span>
  <span class="p">],</span>
<span class="p">})</span>
</code></pre></div></div>

<h3 id="2-jsx-转换index-jsxjs">2. JSX 转换（index-jsx.js）</h3>

<p>使用 <code class="language-plaintext highlighter-rouge">@babel/preset-react</code> 将 JSX 代码转换为普通 JavaScript：</p>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="k">import</span> <span class="nx">Babel</span> <span class="k">from</span> <span class="dl">'</span><span class="s1">@babel/core</span><span class="dl">'</span>
<span class="k">import</span> <span class="nx">presetEnv</span> <span class="k">from</span> <span class="dl">'</span><span class="s1">@babel/preset-env</span><span class="dl">'</span>
<span class="k">import</span> <span class="nx">react</span> <span class="k">from</span> <span class="dl">'</span><span class="s1">@babel/preset-react</span><span class="dl">'</span>

<span class="kd">const</span> <span class="nx">res</span> <span class="o">=</span> <span class="nx">Babel</span><span class="p">.</span><span class="nf">transform</span><span class="p">(</span><span class="nx">jsxFile</span><span class="p">,</span> <span class="p">{</span>
  <span class="na">presets</span><span class="p">:</span> <span class="p">[</span>
    <span class="p">[</span><span class="nx">presetEnv</span><span class="p">,</span> <span class="p">{</span> <span class="na">useBuiltIns</span><span class="p">:</span> <span class="dl">'</span><span class="s1">usage</span><span class="dl">'</span><span class="p">,</span> <span class="na">corejs</span><span class="p">:</span> <span class="mi">3</span> <span class="p">}],</span>
    <span class="nx">react</span>
  <span class="p">],</span>
<span class="p">})</span>
</code></pre></div></div>

<h3 id="3-自定义箭头函数转换插件">3. 自定义箭头函数转换插件</h3>

<p>本项目的插件演示了以下功能：</p>

<ol>
  <li><strong>访问者模式</strong>: 使用 Babel 的访问者模式来遍历和转换 AST 节点</li>
  <li><strong>AST 转换</strong>: 将箭头函数（<code class="language-plaintext highlighter-rouge">=&gt;</code>）转换为常规函数表达式</li>
  <li><strong>类型检查</strong>: 使用 Babel 的 <code class="language-plaintext highlighter-rouge">types</code> 工具进行 AST 节点操作</li>
</ol>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// 转换示例:</span>
<span class="c1">// 输入:</span>
<span class="kd">const</span> <span class="nx">fn</span> <span class="o">=</span> <span class="p">(</span><span class="nx">param</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="nx">param</span> <span class="o">+</span> <span class="mi">10</span><span class="p">;</span>

<span class="c1">// 输出:</span>
<span class="kd">const</span> <span class="nx">fn</span> <span class="o">=</span> <span class="kd">function</span><span class="p">(</span><span class="nx">param</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">return</span> <span class="nx">param</span> <span class="o">+</span> <span class="mi">10</span><span class="p">;</span>
<span class="p">};</span>
</code></pre></div></div>

<h2 id="安装依赖">安装依赖</h2>

<div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code>npm <span class="nb">install</span> @babel/core @babel/preset-env @babel/preset-react core-js@3
</code></pre></div></div>

<h2 id="使用方法">使用方法</h2>

<ol>
  <li>ES6 转换:
    <div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code>node index.js
</code></pre></div>    </div>
  </li>
  <li>JSX 转换:
    <div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code>node index-jsx.js
</code></pre></div>    </div>
  </li>
  <li>箭头函数转换插件:
    <div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code>node index-plugin.js
</code></pre></div>    </div>
  </li>
</ol>

<h2 id="babel-配置说明">Babel 配置说明</h2>

<h3 id="preset-env-配置">preset-env 配置</h3>

<ul>
  <li><strong>useBuiltIns</strong>: 控制 polyfill 的引入方式
    <ul>
      <li><code class="language-plaintext highlighter-rouge">usage</code>: 按需引入，根据代码中使用的特性自动引入所需的 polyfill</li>
      <li><code class="language-plaintext highlighter-rouge">entry</code>: 手动引入全部 polyfill</li>
    </ul>
  </li>
  <li><strong>corejs</strong>: 指定使用的 core-js 版本，推荐使用版本 3</li>
</ul>

<h3 id="preset-react-配置">preset-react 配置</h3>

<ul>
  <li>用于转换 JSX 语法</li>
  <li>可以与 preset-env 配合使用，确保完整的浏览器兼容性</li>
</ul>

<h2 id="核心概念">核心概念</h2>

<ol>
  <li><strong>AST 访问器</strong>: 插件使用访问者模式来匹配和转换 <code class="language-plaintext highlighter-rouge">ArrowFunctionExpression</code> 节点</li>
  <li><strong>节点类型</strong>: 使用 Babel 的类型工具（<code class="language-plaintext highlighter-rouge">t.blockStatement</code>, <code class="language-plaintext highlighter-rouge">t.functionExpression</code> 等）</li>
  <li><strong>路径操作</strong>: 使用 <code class="language-plaintext highlighter-rouge">path.replaceWith()</code> 来替换 AST 中的节点</li>
</ol>

<h2 id="技术细节">技术细节</h2>

<p>转换过程处理以下内容：</p>
<ul>
  <li>函数参数</li>
  <li>函数体（包括块级语句和表达式形式）</li>
  <li>异步箭头函数</li>
  <li>return 语句</li>
</ul>

<p>这个示例展示了 Babel 转换功能的强大之处，以及如何实现自定义的 JavaScript 语法转换。</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>