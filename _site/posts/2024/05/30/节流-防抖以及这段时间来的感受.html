<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>节流、防抖以及这段时间来的感受-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="节流、防抖以及这段时间来的感受"/>
  <meta name="keywords" content="slince,教程"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="节流、防抖以及这段时间来的感受">节流、防抖以及这段时间来的感受</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2024-05-30
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#教程" class="hover-underline">教程</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>这几天复习到了两个概念，一个是节流，一个是防抖，他们都是可以用于浏览器性能优化。</p>

<p>不过在说明节流和防抖的概念之前，先说一下 <code class="language-plaintext highlighter-rouge">call</code>、<code class="language-plaintext highlighter-rouge">apply</code>、<code class="language-plaintext highlighter-rouge">bind</code> 这三个函数</p>

<h2 id="callapplybind">call、apply、bind</h2>

<p>这三个函数都是 <code class="language-plaintext highlighter-rouge">JS</code> 中函数的方法，是用来改变函数调用时的 <code class="language-plaintext highlighter-rouge">this</code> 绑定并传递参数，换句话来说，就是改变 <code class="language-plaintext highlighter-rouge">this</code> 的指向。它允许你在不同的上下文中调用一个函数，这些方法都是 <code class="language-plaintext highlighter-rouge">Function</code> 原型，所以所有函数都可以直接调用它们。</p>

<p>下面来分别看看它们仨</p>

<h3 id="call">call</h3>

<p><code class="language-plaintext highlighter-rouge">call</code> 方法调用一个函数，并指定  <code class="language-plaintext highlighter-rouge">this</code>  的值和单个参数列表。</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nx">func</span><span class="p">.</span><span class="nf">call</span><span class="p">(</span><span class="nx">thisArg</span><span class="p">,</span> <span class="nx">arg1</span><span class="p">,</span> <span class="nx">arg2</span><span class="p">,</span> <span class="p">...)</span>
</code></pre></div></div>

<ul>
  <li><strong><code class="language-plaintext highlighter-rouge">thisArg</code></strong>：调用函数时  <code class="language-plaintext highlighter-rouge">this</code>  的值。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">arg1, arg2, ...</code></strong>：要传递给函数的参数列表。</li>
</ul>

<p><strong>示例</strong>：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">greet</span><span class="p">(</span><span class="nx">intro</span><span class="p">,</span> <span class="nx">time</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="s2">`</span><span class="p">${</span><span class="nx">intro</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">name</span><span class="p">}</span><span class="s2">,</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">age</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="nx">time</span><span class="p">}</span><span class="s2">`</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">person</span> <span class="o">=</span> <span class="p">{</span>
  <span class="na">name</span><span class="p">:</span> <span class="dl">'</span><span class="s1">Tim</span><span class="dl">'</span><span class="p">,</span>
  <span class="na">age</span><span class="p">:</span> <span class="mi">20</span><span class="p">,</span>
<span class="p">}</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">greet</span><span class="p">.</span><span class="nf">call</span><span class="p">(</span><span class="nx">person</span><span class="p">,</span> <span class="dl">'</span><span class="s1">你好</span><span class="dl">'</span><span class="p">,</span> <span class="dl">'</span><span class="s1">2024</span><span class="dl">'</span><span class="p">))</span> <span class="c1">// 你好--Tim,20--2024</span>
</code></pre></div></div>

<p>分析：注意看，return 里面的内容用到了 this，如果没有使用 call 改变 this 指向的话，那么 this 的指向应该是 window。</p>

<h3 id="apply">apply</h3>

<p><code class="language-plaintext highlighter-rouge">apply</code>  方法与  <code class="language-plaintext highlighter-rouge">call</code>  方法类似，但它接受一个参数数组（或类数组对象）作为第二个参数。</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nx">func</span><span class="p">.</span><span class="nf">apply</span><span class="p">(</span><span class="nx">thisArg</span><span class="p">,</span> <span class="p">[</span><span class="nx">argsArray</span><span class="p">])</span>
</code></pre></div></div>

<ul>
  <li><strong><code class="language-plaintext highlighter-rouge">thisArg</code></strong>：调用函数时  <code class="language-plaintext highlighter-rouge">this</code>  的值。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">argsArray</code></strong>：一个数组或类数组对象，包含调用函数时要传递的参数。</li>
</ul>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">greet</span><span class="p">(</span><span class="nx">intro</span><span class="p">,</span> <span class="nx">time</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="s2">`</span><span class="p">${</span><span class="nx">intro</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">name</span><span class="p">}</span><span class="s2">,</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">age</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="nx">time</span><span class="p">}</span><span class="s2">`</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">person</span> <span class="o">=</span> <span class="p">{</span>
  <span class="na">name</span><span class="p">:</span> <span class="dl">'</span><span class="s1">Tim</span><span class="dl">'</span><span class="p">,</span>
  <span class="na">age</span><span class="p">:</span> <span class="mi">20</span><span class="p">,</span>
<span class="p">}</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">greet</span><span class="p">.</span><span class="nf">apply</span><span class="p">(</span><span class="nx">person</span><span class="p">,</span> <span class="p">[</span><span class="dl">'</span><span class="s1">你好</span><span class="dl">'</span><span class="p">,</span> <span class="dl">'</span><span class="s1">2024</span><span class="dl">'</span><span class="p">]))</span> <span class="c1">// 你好--Tim,20--2024</span>
</code></pre></div></div>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">add</span><span class="p">(</span><span class="nx">a</span><span class="p">,</span> <span class="nx">b</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">a</span> <span class="o">+</span> <span class="nx">b</span>
<span class="p">}</span>
<span class="kd">const</span> <span class="nx">numbers</span> <span class="o">=</span> <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">]</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">add</span><span class="p">.</span><span class="nf">apply</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">numbers</span><span class="p">))</span> <span class="c1">// 输出: 5</span>
</code></pre></div></div>

<p>可以看到 apply 和 call 的区别很小，只不过是参数变成了数组</p>

<h3 id="bind">bind</h3>

<p><code class="language-plaintext highlighter-rouge">bind</code>  方法创建一个新的函数，在调用时将  <code class="language-plaintext highlighter-rouge">this</code>  绑定到提供的值，并在新的函数中预设一定的参数。</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">boundFunc</span> <span class="o">=</span> <span class="nx">func</span><span class="p">.</span><span class="nf">bind</span><span class="p">(</span><span class="nx">thisArg</span><span class="p">,</span> <span class="nx">arg1</span><span class="p">,</span> <span class="nx">arg2</span><span class="p">,</span> <span class="p">...)</span>
</code></pre></div></div>

<ul>
  <li><strong><code class="language-plaintext highlighter-rouge">thisArg</code></strong>：调用新函数时  <code class="language-plaintext highlighter-rouge">this</code>  的值。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">arg1, arg2, ...</code></strong>：预设的参数。</li>
</ul>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">greet</span><span class="p">(</span><span class="nx">intro</span><span class="p">,</span> <span class="nx">time</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="s2">`</span><span class="p">${</span><span class="nx">intro</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">name</span><span class="p">}</span><span class="s2">,</span><span class="p">${</span><span class="k">this</span><span class="p">.</span><span class="nx">age</span><span class="p">}</span><span class="s2">--</span><span class="p">${</span><span class="nx">time</span><span class="p">}</span><span class="s2">`</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">person</span> <span class="o">=</span> <span class="p">{</span>
  <span class="na">name</span><span class="p">:</span> <span class="dl">'</span><span class="s1">Tim</span><span class="dl">'</span><span class="p">,</span>
  <span class="na">age</span><span class="p">:</span> <span class="mi">20</span><span class="p">,</span>
<span class="p">}</span>
<span class="kd">const</span> <span class="nx">bindFun</span> <span class="o">=</span> <span class="nx">greet</span><span class="p">.</span><span class="nf">bind</span><span class="p">(</span><span class="nx">person</span><span class="p">,</span> <span class="dl">'</span><span class="s1">你好啊🤔</span><span class="dl">'</span><span class="p">)</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">bindFun</span><span class="p">(</span><span class="dl">'</span><span class="s1">2024~~</span><span class="dl">'</span><span class="p">))</span> <span class="c1">// 你好啊🤔--Tim,20--2024~~</span>
</code></pre></div></div>

<p><strong>总结</strong></p>

<ul>
  <li><strong><code class="language-plaintext highlighter-rouge">call</code></strong>  和  <strong><code class="language-plaintext highlighter-rouge">apply</code></strong>：都用于立即调用函数，并改变  <code class="language-plaintext highlighter-rouge">this</code>  的值。区别在于  <code class="language-plaintext highlighter-rouge">call</code>  接受参数列表，而  <code class="language-plaintext highlighter-rouge">apply</code>  接受参数数组。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">bind</code></strong>：用于创建一个新函数，该函数在调用时将  <code class="language-plaintext highlighter-rouge">this</code>  绑定到提供的值，并可以预设部分参数。新函数不会立即执行，而是可以在稍后调用。</li>
</ul>

<p><strong>使用场景</strong></p>

<ul>
  <li><strong><code class="language-plaintext highlighter-rouge">call</code></strong>：适用于函数立即调用且参数数量固定的情况。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">apply</code></strong>：适用于函数立即调用且参数以数组形式传递的情况，特别是参数数量不固定时。</li>
  <li><strong><code class="language-plaintext highlighter-rouge">bind</code></strong>：适用于创建一个新的函数，并在稍后某个时刻调用该函数，同时预设  <code class="language-plaintext highlighter-rouge">this</code>  绑定和部分参数。</li>
</ul>

<p>接下来我们来自己实现一个 call 方法</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nb">Function</span><span class="p">.</span><span class="nx">prototype</span><span class="p">.</span><span class="nx">myCall</span> <span class="o">=</span> <span class="nf">function </span><span class="p">(</span><span class="nx">context</span><span class="p">)</span> <span class="p">{</span>
  <span class="c1">// 检查调用者是否是函数</span>
  <span class="k">if </span><span class="p">(</span><span class="k">typeof</span> <span class="k">this</span> <span class="o">!==</span> <span class="dl">'</span><span class="s1">function</span><span class="dl">'</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">throw</span> <span class="k">new</span> <span class="nc">TypeError</span><span class="p">(</span><span class="dl">'</span><span class="s1">Error</span><span class="dl">'</span><span class="p">)</span>
  <span class="p">}</span>

  <span class="c1">// 如果没有提供 context，默认使用全局对象（浏览器中是window，这里是非严格模式，严格模式是undefined）</span>
  <span class="nx">context</span> <span class="o">=</span> <span class="nx">context</span> <span class="o">||</span> <span class="nx">windows</span>

  <span class="c1">// 将当前函数（即this）作为 context 对象的一个属性fn，这样，我们就可以通过 context.fn 调用这个函数，并确保 this 指向 context 对象</span>
  <span class="nx">context</span><span class="p">.</span><span class="nx">fn</span> <span class="o">=</span> <span class="k">this</span>

  <span class="c1">// 获取传递的参数</span>
  <span class="c1">// arguments 是一个类数组对象，包含传递给 myCall 的所有参数。使用扩展运算符 ... 将其转换为真正的数组，并使用 slice(1) 方法去掉第一个参数（即 context），获取剩余的参数。</span>
  <span class="kd">const</span> <span class="nx">args</span> <span class="o">=</span> <span class="p">[...</span><span class="nx">args</span><span class="p">].</span><span class="nf">slice</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

  <span class="c1">// 调用函数，并传递参数</span>
  <span class="kd">const</span> <span class="nx">result</span> <span class="o">=</span> <span class="nx">context</span><span class="p">.</span><span class="nf">fn</span><span class="p">(...</span><span class="nx">args</span><span class="p">)</span>

  <span class="c1">// 删除临时添加的属性</span>
  <span class="k">delete</span> <span class="nx">context</span><span class="p">.</span><span class="nx">fn</span>

  <span class="c1">// 返回函数调用的结果</span>
  <span class="k">return</span> <span class="nx">result</span>
<span class="p">}</span>
</code></pre></div></div>

<p>ok，现在你也可以发现，既然它们可以改变 this 的指向，那么就可以用它们来实现节流和防抖了，先来介绍一下节流防抖的概念</p>

<h2 id="节流和防抖">节流和防抖</h2>

<p>节流（Throttle）和防抖（Debounce）是两种用于优化高频率函数调用的技术。它们都可以帮助减少函数的执行次数，从而提升性能，但它们的工作方式和适用场景有所不同。</p>

<h3 id="节流">节流</h3>

<p>节流的作用是在规定的<strong>时间间隔内</strong>，保证一个函数<strong>最多执行一次</strong>。节流常用于处理那些频繁触发的事件，比如滚动、窗口调整大小、鼠标移动等。</p>

<p>我们来看一下具体实现，先来不需要 apply 的版本</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// function 需要被节流的目标函数</span>
<span class="c1">// delay 约定的时间，以毫秒为单位。在这个时间间隔内，目标函数最多只能执行一次</span>
<span class="kd">function</span> <span class="nf">throttle</span><span class="p">(</span><span class="nx">func</span><span class="p">,</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
  <span class="c1">// 记录上一次目标函数被调用的时间戳，初始值为0。</span>
  <span class="kd">let</span> <span class="nx">lastCall</span> <span class="o">=</span> <span class="mi">0</span>

  <span class="c1">// 返回一个新的匿名函数，这个函数包裹了目标函数 func 并添加了节流逻辑。</span>
  <span class="c1">// ...args：使用 ES6 的扩展运算符（spread operator）来获取传给这个匿名函数的参数，并将这些参数传递给 func。</span>
  <span class="k">return</span> <span class="nf">function </span><span class="p">(...</span><span class="nx">args</span><span class="p">)</span> <span class="p">{</span>
    <span class="c1">// 获取当前时间</span>
    <span class="kd">const</span> <span class="nx">now</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Date</span><span class="p">().</span><span class="nf">getTime</span><span class="p">()</span>

    <span class="c1">// 检查时间间隔，如果当前时间减去上次触发时间小于约定的时间段，直接返回；也就是说，用户在这个时间段内触发了多次</span>
    <span class="c1">// 但是由于还在这个时间段内，所以我们只执行一次</span>
    <span class="k">if </span><span class="p">(</span><span class="nx">now</span> <span class="o">-</span> <span class="nx">lastCall</span> <span class="o">&lt;</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span>
    <span class="p">}</span>

    <span class="nx">lastCall</span> <span class="o">=</span> <span class="nx">now</span>

    <span class="c1">// 将参数传递给目标函数，并调用它</span>
    <span class="k">return</span> <span class="nf">func</span><span class="p">(...</span><span class="nx">args</span><span class="p">)</span>
  <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<p>下面是一个完整的使用用例，当用户拖动窗口的时候，在约定的 200 毫秒内，控制台只打印一次</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">onResize</span><span class="p">()</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="dl">'</span><span class="s1">Window resized</span><span class="dl">'</span><span class="p">,</span> <span class="k">new</span> <span class="nc">Date</span><span class="p">().</span><span class="nf">getTime</span><span class="p">())</span>
<span class="p">}</span>
<span class="kd">function</span> <span class="nf">throttle</span><span class="p">(</span><span class="nx">func</span><span class="p">,</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">let</span> <span class="nx">lastCall</span> <span class="o">=</span> <span class="mi">0</span>
  <span class="k">return</span> <span class="nf">function </span><span class="p">()</span> <span class="p">{</span>
    <span class="kd">const</span> <span class="nx">now</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Date</span><span class="p">().</span><span class="nf">getTime</span><span class="p">()</span>
    <span class="k">if </span><span class="p">(</span><span class="nx">now</span> <span class="o">-</span> <span class="nx">lastCall</span> <span class="o">&lt;</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span>
    <span class="p">}</span>
    <span class="nx">lastCall</span> <span class="o">=</span> <span class="nx">now</span>
    <span class="k">return</span> <span class="nf">func</span><span class="p">()</span>
  <span class="p">}</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">throttledResize</span> <span class="o">=</span> <span class="nf">throttle</span><span class="p">(</span><span class="nx">onResize</span><span class="p">,</span> <span class="mi">200</span><span class="p">)</span>

<span class="nb">window</span><span class="p">.</span><span class="nf">addEventListener</span><span class="p">(</span><span class="dl">'</span><span class="s1">resize</span><span class="dl">'</span><span class="p">,</span> <span class="nx">throttledResize</span><span class="p">)</span>
</code></pre></div></div>

<p>接下来我们用 apply 函数实现一下节流</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">throttle</span><span class="p">(</span><span class="nx">func</span><span class="p">,</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">let</span> <span class="nx">lastCall</span> <span class="o">=</span> <span class="mi">0</span>
  <span class="k">return</span> <span class="nf">function </span><span class="p">(...</span><span class="nx">args</span><span class="p">)</span> <span class="p">{</span>
    <span class="kd">const</span> <span class="nx">now</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Date</span><span class="p">().</span><span class="nf">getTime</span><span class="p">()</span>
    <span class="k">if </span><span class="p">(</span><span class="nx">now</span> <span class="o">-</span> <span class="nx">lastCall</span> <span class="o">&lt;</span> <span class="nx">delay</span><span class="p">)</span> <span class="p">{</span>
      <span class="k">return</span>
    <span class="p">}</span>
    <span class="nx">lastCall</span> <span class="o">=</span> <span class="nx">now</span>
    <span class="k">return</span> <span class="nx">func</span><span class="p">.</span><span class="nf">apply</span><span class="p">(</span><span class="k">this</span><span class="p">,</span> <span class="nx">args</span><span class="p">)</span>
  <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<p>这两种实现方式，核心逻辑上面完全一样，区别只是在参数传递上</p>

<ul>
  <li>使用 apply 方法更适合已经有参数数组的情况，并且需要显式指定 this 上下文。</li>
  <li>使用扩展运算符 …args 更简洁，适合直接处理参数列表且不需要显式指定 this 上下文的情况。</li>
</ul>

<h3 id="防抖">防抖</h3>

<p>防抖（Debounce）是一种用于优化高频率函数调用的技术，它确保一个函数在事件结束后的<strong>指定时间内</strong>只执行<strong>一次</strong>。如果在这段时间内再次触发事件，则重新计时。防抖技术常用于处理那些需要在用户停止操作后执行的事件，比如输入框提示、表单验证等。</p>

<p>原理：
防抖通过设置一个延迟时间（如 200 毫秒），在事件触发后开始计时。如果在延迟时间内再次触发事件，则清除之前的计时器并重新开始计时。只有当延迟时间过去后没有再次触发事件，函数才会执行。</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">debounce</span><span class="p">(</span><span class="nx">func</span><span class="p">,</span> <span class="nx">wait</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">let</span> <span class="nx">timeout</span>
  <span class="k">return</span> <span class="nf">function </span><span class="p">(...</span><span class="nx">args</span><span class="p">)</span> <span class="p">{</span>
    <span class="nf">clearTimeout</span><span class="p">(</span><span class="nx">timeout</span><span class="p">)</span>

    <span class="c1">// 设置一个新的计时器，在延迟时间 wait 之后调用目标函数 func。</span>
    <span class="c1">// 使用箭头函数（=&gt;）确保 this 的值在回调中与外层函数一致。</span>
    <span class="c1">// func.apply(this, args)：使用 apply 方法调用目标函数 func，并确保 this 上下文和参数 args 被正确传递。</span>
    <span class="nx">timeout</span> <span class="o">=</span> <span class="nf">setTimeout</span><span class="p">(()</span> <span class="o">=&gt;</span> <span class="p">{</span>
      <span class="nx">func</span><span class="p">.</span><span class="nf">apply</span><span class="p">(</span><span class="k">this</span><span class="p">,</span> <span class="nx">args</span><span class="p">)</span>
    <span class="p">},</span> <span class="nx">wait</span><span class="p">)</span>
  <span class="p">}</span>
<span class="p">}</span>
</code></pre></div></div>

<p>示例：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">onInput</span><span class="p">()</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="dl">'</span><span class="s1">Input event</span><span class="dl">'</span><span class="p">,</span> <span class="k">new</span> <span class="nc">Date</span><span class="p">().</span><span class="nf">toISOString</span><span class="p">())</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">debouncedInput</span> <span class="o">=</span> <span class="nf">debounce</span><span class="p">(</span><span class="nx">onInput</span><span class="p">,</span> <span class="mi">300</span><span class="p">)</span>

<span class="kd">const</span> <span class="nx">inputElement</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nf">querySelector</span><span class="p">(</span><span class="dl">'</span><span class="s1">input</span><span class="dl">'</span><span class="p">)</span>
<span class="nx">inputElement</span><span class="p">.</span><span class="nf">addEventListener</span><span class="p">(</span><span class="dl">'</span><span class="s1">input</span><span class="dl">'</span><span class="p">,</span> <span class="nx">debouncedInput</span><span class="p">)</span>
</code></pre></div></div>
<p>onInput 函数只有在用户停止输入 300 毫秒后才会执行。如果用户在 300 毫秒内继续输入，计时器将重新开始。</p>

<h2 id="好了来聊聊最近的感受吧">好了，来聊聊最近的感受吧</h2>

<p>说实话，这段时间过得并不好，该从哪里说起呢，就从这篇文章来说起吧。其实今年我一直想写文章，不管是技术学习上的还是生活上的，但是我今年一直在找机会，找一个新的工作；所以我心想，等我找到一个工作之后，就开始写文章，因为我可以学习公司的业务，没准可以产出更好的文章。</p>

<p>但是我的希望落空了，年后我就一直在陆陆续续的投递简历，但是没什么回应，后来我开始学习 react，看了很多教程，也做了一些笔记，都存放在我的 obsidian 上了，上个月22号，我开始写一个react项目<a href="https://github.com/slince-zero/IMaker">IMaker</a>，我花了一周左右的时间去写，到现在我也在修改commit，到今天这个项目在GitHub上大概有 200star 了。</p>

<p>这是我第一次获得这么多的star，心中有些窃喜，我大概花了两周左右就到了100，于是我赶紧修改简历，加上这个新的项目，开始投递简历，第一周有两个面试，一家是web3，一家是小红书的外包，我感觉自己答的还不错，基本都回答上了，可是就是没有下文。</p>

<p>第二周，没有任何回应，我又开始有些焦虑了，第三周没有回应，我不知道是哪里出了问题，也许确实是我的业务能力和技术不太行。我开始怀疑自己，甚至有些抱怨，但后来我意识到，这样下去没有任何的用处，除了消耗自己，还是消耗自己。</p>

<p>前两天做了一个线上测试，有几道算法题没有写出来，不出意料，挂了，但是我并没有太过失落，至少我知道了自己的一些短板，我想在后面的时间里去练习算题题，虽然我真的很不喜欢算法题。</p>

<p>这几天我听到一些话语，大概意思就是，“你觉得自己不幸，可你没有跟那些真正不幸的人对比过”。</p>

<p>我想了想，确实是这样，我只不过是暂时找不到一份合适的工作，四肢健全，身体也没有什么太大的问题，已经十分幸运、十分幸运、十分幸运了。</p>

<p>我来到这个世上不是为了内耗自己，即使生活过得不怎样，可我保持对生活的乐观，不矢为一件幸事。</p>

<p><del>我人目前在北京，只有一年多的经验，想找一份前端的工作，技术栈是 vue3 和 react</del></p>

<p><del>这是我的<a href="https://github.com/slince-zero/resume/blob/main/resume.pdf">简历</a></del></p>

<p>我的邮箱是：<EMAIL></p>

<p><del>如果您有工作机会，欢迎联系我</del></p>

<p>如果您有任何想问的，或者想跟聊聊，欢迎邮件联系我。</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>