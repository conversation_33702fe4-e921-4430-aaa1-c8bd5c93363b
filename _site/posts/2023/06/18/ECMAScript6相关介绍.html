<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>ES6相关介绍-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="ES6相关介绍"/>
  <meta name="keywords" content="slince,教程"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="ES6相关介绍">ES6相关介绍</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2023-06-18
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#教程" class="hover-underline">教程</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <h3 id="ecmascript相关介绍">ECMAScript相关介绍</h3>

<ol>
  <li>
    <p>什么是ECMA
ECMA（European Computer Manufacturers Association）中文名称为欧洲计算机制造商协会，这个组织的目标是评估、开发和认可电信和计算机标准，1994 年后该组织改名为Ecma 国际</p>
  </li>
  <li>
    <p>什么是ECMAScript
ECMAScript 是E尺码国际通过ECMA-262标准化的脚本程序设计语言。ECMAScript定义了一套脚本语言的标准，规定了语法、类型、语句、关键字、保留字、操作符、对象等方面的规范。JavaScript是ECMAScript的一种实现，同时还包括了浏览器和Node.js等环境提供的API和对象。由于ECMAScript是一种标准化的语言，所以不同的JavaScript实现都应该遵循ECMAScript标准，以确保代码的可移植性和互操作性。</p>
  </li>
</ol>

<h3 id="ecmascript-6-新特性">ECMAScript 6 新特性</h3>

<h4 id="1-let-关键字">1. let 关键字</h4>

<p>let 关键字用来声明变量，使用let声明的变量有以下特点：</p>

<ul>
  <li>不允许重复声明</li>
  <li>块级作用域</li>
  <li>不存在变量提升</li>
  <li>不影响作用域链
应用场景：以后声明变量用 let 就行</li>
</ul>

<h4 id="2-cosnt-关键字">2. cosnt 关键字</h4>

<p>const 关键字用来声明常量，从const声明有以下特点：</p>

<ul>
  <li>声明必须赋初始值</li>
  <li>标识符一般为大写</li>
  <li>不允许重复声明</li>
  <li>值不允许修改</li>
  <li>块级作用域</li>
</ul>

<p>注意：对象属性修改和元素变化不会发出const错误
应用场景：声明对象类型使用const，非对象类型声明选择let</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">arr</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">];</span>
<span class="nx">arr</span><span class="p">.</span><span class="nf">push</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">);</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">arr</span><span class="p">);</span> <span class="c1">// 不报错</span>

<span class="kd">const</span> <span class="nx">obj</span> <span class="o">=</span> <span class="p">{</span>
<span class="na">uanme</span><span class="p">:</span> <span class="dl">'</span><span class="s1">rick</span><span class="dl">'</span><span class="p">,</span>
<span class="na">age</span><span class="p">:</span> <span class="mi">20</span><span class="p">;</span>
<span class="p">}</span>
<span class="nx">obj</span><span class="p">.</span><span class="nx">age</span> <span class="o">=</span> <span class="mi">11</span><span class="p">;</span> <span class="c1">// 只要不改变地址，就不报错</span>
</code></pre></div></div>

<h4 id="3-变量的解构赋值">3. 变量的解构赋值</h4>

<p>ES6允许按照一定模式，从数组和对象中提取值，对变量进行赋值，这被称为 结构赋值</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// 以下是一个使用数组解构赋值案例：</span>
<span class="kd">const</span> <span class="nx">arr</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>
<span class="kd">const</span> <span class="p">[</span><span class="nx">a</span><span class="p">,</span> <span class="nx">b</span><span class="p">,</span> <span class="nx">c</span><span class="p">]</span> <span class="o">=</span> <span class="nx">arr</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">a</span><span class="p">);</span> <span class="c1">// 1</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">b</span><span class="p">);</span> <span class="c1">// 2</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">c</span><span class="p">);</span> <span class="c1">// 3</span>
</code></pre></div></div>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// 以下是一个使用对象解构赋值案例：</span>
<span class="kd">const</span> <span class="nx">obj</span> <span class="o">=</span> <span class="p">{</span> <span class="na">x</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="na">y</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="na">z</span><span class="p">:</span> <span class="mi">3</span> <span class="p">};</span>
<span class="kd">const</span> <span class="p">{</span> <span class="nx">x</span><span class="p">,</span> <span class="nx">y</span><span class="p">,</span> <span class="nx">z</span> <span class="p">}</span> <span class="o">=</span> <span class="nx">obj</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">x</span><span class="p">);</span> <span class="c1">// 1</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">y</span><span class="p">);</span> <span class="c1">// 2</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">z</span><span class="p">);</span> <span class="c1">// 3</span>

</code></pre></div></div>

<h4 id="4-模板字符串">4. 模板字符串</h4>

<p>模板字符串是增强版的字符串，用反引号来标识，特点：</p>

<ul>
  <li>字符串可以出现换行符</li>
  <li>可以使用 ${xxx} 形式输出变量</li>
</ul>

<p>应用场景：当遇到字符串与变量拼接的情况使用模板字符串</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">let</span> <span class="nx">name</span> <span class="o">=</span> <span class="dl">'</span><span class="s1">jack</span><span class="dl">'</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="s2">`hello, </span><span class="p">${</span><span class="nx">name</span><span class="p">}</span><span class="s2">`</span><span class="p">);</span>

<span class="kd">let</span> <span class="nx">ul</span> <span class="o">=</span> 
<span class="s2">`
  &lt;ul&gt;
   &lt;li&gt;1&lt;/li&gt;
   &lt;li&gt;2&lt;/li&gt;
   &lt;li&gt;3&lt;/li&gt;
  &lt;/ul&gt;
`</span>
</code></pre></div></div>

<h4 id="5-简化对象写法">5. 简化对象写法</h4>

<p>在 ES6 中，我们可以使用简化对象写法来定义对象。这种写法可以让我们更加简洁地定义对象，避免了重复书写属性名和属性值</p>

<p>应用场景：以后就用简写就行</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">name</span> <span class="o">=</span> <span class="dl">'</span><span class="s1">John</span><span class="dl">'</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">age</span> <span class="o">=</span> <span class="mi">30</span><span class="p">;</span>

<span class="kd">const</span> <span class="nx">person</span> <span class="o">=</span> <span class="p">{</span>
  <span class="nx">name</span><span class="p">,</span>
  <span class="nx">age</span>
<span class="p">};</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">person</span><span class="p">);</span> <span class="c1">// { name: 'John', age: 30 }</span>

<span class="c1">// 在上面的代码中，我们定义了一个 `person` 对象，使用了简化对象写法。我们只需要写出属性名，然后将变量名赋值给属性名即可。这样就可以定义一个具有 `name` 和 `age` 属性的对象。</span>
</code></pre></div></div>

<h4 id="6-箭头函数">6. 箭头函数</h4>

<p>ES6允许使用 <strong>=&gt;</strong> 定义函数</p>

<ul>
  <li>function 写法
    <div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">fn</span><span class="p">(</span><span class="nx">param1</span><span class="p">,</span> <span class="nx">param2</span><span class="p">...)</span> <span class="p">{</span>
<span class="c1">// 函数体</span>
<span class="k">return</span> <span class="nx">expression</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>    </div>
  </li>
  <li>=&gt; 写法
    <div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">let</span> <span class="nx">fn</span> <span class="o">=</span> <span class="p">(</span><span class="nx">param1</span><span class="p">,</span> <span class="nx">param2</span><span class="p">...)</span> <span class="o">=&gt;</span><span class="p">{</span>
 <span class="c1">// 函数体</span>
 <span class="k">return</span> <span class="nx">expression</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>    </div>
  </li>
</ul>

<p>注意：</p>

<ul>
  <li>如果形参只有一个，小括号可以省略</li>
  <li>如果函数体只有一条语句，花括号可以省略，函数的返回值为该条语句的执行结果</li>
  <li><strong>箭头函数this是静态的，时钟指向声明时所在作用域下this的值</strong>
```js
// 用箭头函数定义
const person = {
name: ‘Alice’,
sayName: () =&gt; {
  console.log(this.name);
}
};</li>
</ul>

<p>person.sayName();
//在这个例子中，箭头函数sayName被定义在person对象内部，因此它的this指向是person对象所在的作用域下的this值，即全局的this值。因此，当我们调用person.sayName()时，它会输出undefined。</p>

<p>// 用普通函数定义
const person = {
  name: ‘Alice’,
  sayName: function() {
    console.log(this.name);
  }
};</p>

<p>person.sayName();
//在这个例子中，sayName方法使用普通函数来定义，因此它的this指向是动态的，即指向调用该方法的对象，也就是person对象。因此，当我们调用person.sayName()时，它会输出Alice。</p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>
* **箭头函数不能作为构造函数实例化**
```js
// 箭头函数，不能被用作构造函数来创建实例化对象。这是因为箭头函数没有自己的 this 值，它的 this 值是继承自它所在的上下文
const Person = (name) =&gt; {
  this.name = name;
};

const john = new Person('John'); // TypeError: Person is not a constructor

function Person(name) {
  this.name = name;
}



// 普通函数
const john = new Person('John');
console.log(john.name); // "John"

</code></pre></div></div>

<ul>
  <li><strong>箭头函数不能使用 <code class="language-plaintext highlighter-rouge">arguments</code> 对象</strong>
<code class="language-plaintext highlighter-rouge">arguments</code> 对象是一个类数组对象，它包含了函数调用时传入的所有参数。在传统函数中，我们可以通过 <code class="language-plaintext highlighter-rouge">arguments</code> 对象来获取这些参数。但是，在箭头函数中，<code class="language-plaintext highlighter-rouge">arguments</code> 对象并不存在，因为箭头函数没有自己的 <code class="language-plaintext highlighter-rouge">this</code> 和 <code class="language-plaintext highlighter-rouge">arguments</code> 对象，它们会继承父级作用域的 <code class="language-plaintext highlighter-rouge">this</code> 和 <code class="language-plaintext highlighter-rouge">arguments</code> 对象</li>
</ul>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">traditionalFunction</span><span class="p">()</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">arguments</span><span class="p">);</span>
<span class="p">}</span>

<span class="kd">const</span> <span class="nx">arrowFunction</span> <span class="o">=</span> <span class="p">()</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">arguments</span><span class="p">);</span>
<span class="p">}</span>

<span class="nf">traditionalFunction</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">);</span> <span class="c1">// 输出 [1, 2, 3]</span>
<span class="nf">arrowFunction</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">);</span> <span class="c1">// 报错：arguments is not defined</span>

<span class="c1">// 在这个例子中，我们定义了一个传统函数 `traditionalFunction` 和一个箭头函数 `arrowFunction`，并且在它们内部分别打印了 `arguments` 对象。当我们调用 `traditionalFunction` 时，它会输出 `[1, 2, 3]`，因为 `arguments` 对象存在并包含了传入的参数。但是，当我们调用 `arrowFunction` 时，它会抛出一个错误，因为 `arguments` 对象不存在</span>


<span class="c1">// 因此，如果我们需要在箭头函数中获取参数，我们可以使用剩余参数语法 `...args` 来代替 `arguments` 对象。</span>
<span class="kd">const</span> <span class="nx">arrowFunction</span> <span class="o">=</span> <span class="p">(...</span><span class="nx">args</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">args</span><span class="p">);</span>
<span class="p">}</span>

<span class="nf">arrowFunction</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">);</span> <span class="c1">// 输出 [1, 2, 3]</span>

</code></pre></div></div>

<h4 id="7-剩余参数rest">7. 剩余参数rest</h4>

<p>JavaScript 的剩余参数语法（rest parameter syntax）用三个点（…）表示，用于将函数的多个参数收集成一个数组</p>

<p>例如，以下函数的参数列表中使用了剩余参数语法：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">sum</span><span class="p">(...</span><span class="nx">numbers</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">numbers</span><span class="p">.</span><span class="nf">reduce</span><span class="p">((</span><span class="nx">total</span><span class="p">,</span> <span class="nx">num</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="nx">total</span> <span class="o">+</span> <span class="nx">num</span><span class="p">,</span> <span class="mi">0</span><span class="p">);</span>
<span class="p">}</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">sum</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">));</span> <span class="c1">// 6</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">sum</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">));</span> <span class="c1">// 22</span>
</code></pre></div></div>

<p>在这个例子中，<code class="language-plaintext highlighter-rouge">sum</code> 函数的参数列表中使用了剩余参数语法，将传入函数的所有参数收集成一个名为 <code class="language-plaintext highlighter-rouge">numbers</code> 的数组。函数内部使用 <code class="language-plaintext highlighter-rouge">reduce</code> 方法对数组中的所有元素求和，并返回求和结果</p>

<p><strong>需要注意的是，剩余参数语法只能用于函数的最后一个参数</strong></p>

<p>当我们在定义一个函数时，可以使用剩余参数语法来表示函数可以接受任意数量的参数。但是需要注意的是，剩余参数语法只能用于函数的最后一个参数</p>

<p>举个例子，假设我们要定义一个函数，用于计算任意数量的数字的平均值。我们可以使用剩余参数语法来实现：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">average</span><span class="p">(...</span><span class="nx">numbers</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">let</span> <span class="nx">sum</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
  <span class="k">for </span><span class="p">(</span><span class="kd">let</span> <span class="nx">number</span> <span class="k">of</span> <span class="nx">numbers</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">sum</span> <span class="o">+=</span> <span class="nx">number</span><span class="p">;</span>
  <span class="p">}</span>
  <span class="k">return</span> <span class="nx">sum</span> <span class="o">/</span> <span class="nx">numbers</span><span class="p">.</span><span class="nx">length</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<p>在这个例子中，<code class="language-plaintext highlighter-rouge">...numbers</code>表示可以接受任意数量的参数，并将它们存储在一个数组中。我们可以像这样调用这个函数：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">average</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">));</span> <span class="c1">// 输出 2</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">average</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">7</span><span class="p">));</span> <span class="c1">// 输出 5.5</span>
</code></pre></div></div>

<p>需要注意的是，剩余参数语法只能用于函数的最后一个参数。例如，下面这个函数定义是错误的：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">example</span><span class="p">(...</span><span class="nx">numbers</span><span class="p">,</span> <span class="nx">x</span><span class="p">)</span> <span class="p">{</span>
  <span class="c1">// 错误的函数定义，剩余参数语法不能用于函数的最后一个参数之前</span>
<span class="p">}</span>
</code></pre></div></div>

<h4 id="8-函数参数默认值设定">8. 函数参数默认值设定</h4>

<p>ES6允许给函数参数设置默认值，当调用函数时不给实参，则使用参数默认值，有默认值的形参一般要靠后</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">let</span> <span class="nx">add</span> <span class="o">=</span> <span class="p">(</span><span class="nx">x</span><span class="p">,</span> <span class="nx">y</span><span class="p">,</span> <span class="nx">z</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="nx">x</span> <span class="o">+</span> <span class="nx">y</span> <span class="o">+</span><span class="nx">z</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">add</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">));</span> <span class="c1">// 6</span>
</code></pre></div></div>

<h4 id="9-spread扩展运算符">9. Spread扩展运算符</h4>

<p>JS中的扩展运算符（spread operator）是三个点（…），它可以将一个数组或对象“展开”成多个独立的值。下面是一些扩展运算符的使用示例：</p>

<ol>
  <li>将数组展开成函数参数：</li>
</ol>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">arr</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(...</span><span class="nx">arr</span><span class="p">);</span> <span class="c1">// 1 2 3</span>

<span class="kd">function</span> <span class="nf">sum</span><span class="p">(</span><span class="nx">a</span><span class="p">,</span> <span class="nx">b</span><span class="p">,</span> <span class="nx">c</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="nx">a</span> <span class="o">+</span> <span class="nx">b</span> <span class="o">+</span> <span class="nx">c</span><span class="p">;</span>
<span class="p">}</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nf">sum</span><span class="p">(...</span><span class="nx">arr</span><span class="p">));</span> <span class="c1">// 6</span>
</code></pre></div></div>

<ol>
  <li>合并数组：</li>
</ol>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">arr1</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>
<span class="kd">const</span> <span class="nx">arr2</span> <span class="o">=</span> <span class="p">[</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">];</span>
<span class="kd">const</span> <span class="nx">arr3</span> <span class="o">=</span> <span class="p">[...</span><span class="nx">arr1</span><span class="p">,</span> <span class="p">...</span><span class="nx">arr2</span><span class="p">];</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">arr3</span><span class="p">);</span> <span class="c1">// [1, 2, 3, 4, 5, 6]</span>
</code></pre></div></div>

<ol>
  <li>复制数组：</li>
</ol>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">arr1</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>
<span class="kd">const</span> <span class="nx">arr2</span> <span class="o">=</span> <span class="p">[...</span><span class="nx">arr1</span><span class="p">];</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">arr2</span><span class="p">);</span> <span class="c1">// [1, 2, 3]</span>
</code></pre></div></div>

<ol>
  <li>将对象展开成另一个对象：</li>
</ol>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">obj1</span> <span class="o">=</span> <span class="p">{</span> <span class="na">a</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="na">b</span><span class="p">:</span> <span class="mi">2</span> <span class="p">};</span>
<span class="kd">const</span> <span class="nx">obj2</span> <span class="o">=</span> <span class="p">{</span> <span class="p">...</span><span class="nx">obj1</span><span class="p">,</span> <span class="na">c</span><span class="p">:</span> <span class="mi">3</span> <span class="p">};</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">obj2</span><span class="p">);</span> <span class="c1">// { a: 1, b: 2, c: 3 }</span>
</code></pre></div></div>

<ol>
  <li>复制对象：</li>
</ol>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">obj1</span> <span class="o">=</span> <span class="p">{</span> <span class="na">a</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="na">b</span><span class="p">:</span> <span class="mi">2</span> <span class="p">};</span>
<span class="kd">const</span> <span class="nx">obj2</span> <span class="o">=</span> <span class="p">{</span> <span class="p">...</span><span class="nx">obj1</span> <span class="p">};</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">obj2</span><span class="p">);</span> <span class="c1">// { a: 1, b: 2 }</span>
</code></pre></div></div>

<h4 id="10-symbol">10. Symbol</h4>

<p>JavaScript 中的 Symbol 是一种基本数据类型，用于创建唯一且不可变的值，通常用作对象属性的键名。Symbol 值可以通过 Symbol() 函数进行创建，如下所示：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">mySymbol</span> <span class="o">=</span> <span class="nc">Symbol</span><span class="p">();</span>
</code></pre></div></div>

<p>Symbol 值可以作为对象的属性键名，如下所示：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">mySymbol</span> <span class="o">=</span> <span class="nc">Symbol</span><span class="p">();</span>
<span class="kd">const</span> <span class="nx">myObj</span> <span class="o">=</span> <span class="p">{};</span>
<span class="nx">myObj</span><span class="p">[</span><span class="nx">mySymbol</span><span class="p">]</span> <span class="o">=</span> <span class="dl">'</span><span class="s1">Hello World</span><span class="dl">'</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">myObj</span><span class="p">[</span><span class="nx">mySymbol</span><span class="p">]);</span> <span class="c1">// 输出 'Hello World'</span>
</code></pre></div></div>

<p>由于每个 Symbol 值都是唯一的，因此它们可以用于创建私有属性或方法，以避免命名冲突。例如，我们可以使用 Symbol 值作为对象的私有属性，如下所示：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">mySymbol</span> <span class="o">=</span> <span class="nc">Symbol</span><span class="p">(</span><span class="dl">'</span><span class="s1">myPrivateProperty</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">class</span> <span class="nc">MyClass</span> <span class="p">{</span>
  <span class="nf">constructor</span><span class="p">()</span> <span class="p">{</span>
    <span class="k">this</span><span class="p">[</span><span class="nx">mySymbol</span><span class="p">]</span> <span class="o">=</span> <span class="dl">'</span><span class="s1">This is a private property</span><span class="dl">'</span><span class="p">;</span>
  <span class="p">}</span>
  <span class="nf">getPrivateProperty</span><span class="p">()</span> <span class="p">{</span>
    <span class="k">return</span> <span class="k">this</span><span class="p">[</span><span class="nx">mySymbol</span><span class="p">];</span>
  <span class="p">}</span>
<span class="p">}</span>
<span class="kd">const</span> <span class="nx">myObj</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">MyClass</span><span class="p">();</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">myObj</span><span class="p">.</span><span class="nf">getPrivateProperty</span><span class="p">());</span> <span class="c1">// 输出 'This is a private property'</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">myObj</span><span class="p">[</span><span class="nx">mySymbol</span><span class="p">]);</span> <span class="c1">// 输出 undefined，因为 mySymbol 是私有属性，无法直接访问</span>
</code></pre></div></div>

<p>除了使用 Symbol() 函数创建 Symbol 值外，还可以使用 Symbol.for() 函数创建全局共享的 Symbol 值。例如，我们可以使用 Symbol.for() 函数创建一个全局共享的 Symbol 值，并将其作为对象的属性键名，如下所示：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">myGlobalSymbol</span> <span class="o">=</span> <span class="nb">Symbol</span><span class="p">.</span><span class="k">for</span><span class="p">(</span><span class="dl">'</span><span class="s1">myGlobalSymbol</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">myObj</span> <span class="o">=</span> <span class="p">{};</span>
<span class="nx">myObj</span><span class="p">[</span><span class="nx">myGlobalSymbol</span><span class="p">]</span> <span class="o">=</span> <span class="dl">'</span><span class="s1">Hello World</span><span class="dl">'</span><span class="p">;</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">myObj</span><span class="p">[</span><span class="nx">myGlobalSymbol</span><span class="p">]);</span> <span class="c1">// 输出 'Hello World'</span>
</code></pre></div></div>

<p>注意，使用 Symbol.for() 函数创建的 Symbol 值会被添加到全局符号注册表中，可以在不同的代码文件中共享和访问</p>

<h4 id="11-迭代器">11. 迭代器</h4>

<p>在 JavaScript 中，Symbol 是一种新的<strong>原始数据类型</strong>，它可以用来创建唯一的标识符，这些标识符可以用于对象的属性名或者其他需要唯一标识符的场合。而<strong>迭代器</strong>（Iterator）是一种<strong>对象</strong>，它提供了一种访问集合（如数组、对象和字符串等）中元素的方式，可以逐个访问集合中的元素，而不需要了解集合的内部实现。Symbol 和迭代器之间的关系是，迭代器使用 Symbol.iterator 属性来定义自己的迭代器方法，该方法返回一个迭代器对象，该对象包含一个 next() 方法，用于迭代集合中的元素。</p>

<p>举个例子，假设我们有一个数组，我们可以使用 Symbol.iterator 属性来定义一个迭代器方法，该方法返回一个迭代器对象，我们可以使用该对象的 next() 方法来逐个访问数组中的元素。下面是一个简单的例子：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">arr</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>
<span class="kd">const</span> <span class="nx">iterator</span> <span class="o">=</span> <span class="nx">arr</span><span class="p">[</span><span class="nb">Symbol</span><span class="p">.</span><span class="nx">iterator</span><span class="p">]();</span>

<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">iterator</span><span class="p">.</span><span class="nf">next</span><span class="p">());</span> <span class="c1">// { value: 1, done: false }</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">iterator</span><span class="p">.</span><span class="nf">next</span><span class="p">());</span> <span class="c1">// { value: 2, done: false }</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">iterator</span><span class="p">.</span><span class="nf">next</span><span class="p">());</span> <span class="c1">// { value: 3, done: false }</span>
<span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">iterator</span><span class="p">.</span><span class="nf">next</span><span class="p">());</span> <span class="c1">// { value: undefined, done: true }</span>
</code></pre></div></div>

<p>在上面的例子中，我们首先获取了数组的迭代器对象，然后使用该对象的 next() 方法来逐个访问数组中的元素，直到所有元素都被访问完毕。每次调用 next() 方法时，都会返回一个包含当前元素值和是否遍历完成的对象。当遍历完成时，done 属性为 true，value 属性为 undefined</p>

<p>需要注意的是，Symbol.iterator 属性只能在可迭代对象上使用，即实现了 @@iterator 方法的对象。常见的可迭代对象包括数组、Set、Map、字符串等</p>

<p>除了数组，ES6 还为许多内置集合类型（如 Set、Map 等）和自定义对象提供了迭代器支持。我们可以使用 for…of 循环来遍历这些集合类型中的元素，例如：</p>
<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">mySet</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Set</span><span class="p">([</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">]);</span>
<span class="k">for </span><span class="p">(</span><span class="kd">const</span> <span class="nx">value</span> <span class="k">of</span> <span class="nx">mySet</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">value</span><span class="p">);</span>
<span class="p">}</span>
<span class="c1">// Output: 1 2 3</span>

<span class="kd">const</span> <span class="nx">myMap</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Map</span><span class="p">([[</span><span class="dl">'</span><span class="s1">a</span><span class="dl">'</span><span class="p">,</span> <span class="mi">1</span><span class="p">],</span> <span class="p">[</span><span class="dl">'</span><span class="s1">b</span><span class="dl">'</span><span class="p">,</span> <span class="mi">2</span><span class="p">],</span> <span class="p">[</span><span class="dl">'</span><span class="s1">c</span><span class="dl">'</span><span class="p">,</span> <span class="mi">3</span><span class="p">]]);</span>
<span class="k">for </span><span class="p">(</span><span class="kd">const</span> <span class="p">[</span><span class="nx">key</span><span class="p">,</span> <span class="nx">value</span><span class="p">]</span> <span class="k">of</span> <span class="nx">myMap</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">key</span><span class="p">,</span> <span class="nx">value</span><span class="p">);</span>
<span class="p">}</span>
<span class="c1">// Output: a 1 b 2 c 3</span>
</code></pre></div></div>

<p>在这些例子中，我们使用 for…of 循环来遍历集合类型中的元素。由于这些集合类型都实现了迭代器，因此我们可以直接使用 for…of 循环来遍历它们中的元素，而不需要手动调用 next() 方法</p>

<h5 id="工作原理">工作原理</h5>

<ol>
  <li>创建一个指针对象，指向当前数据解构的起始位置</li>
  <li>第一次调用对象的next方法，指针自动指向数据解构的第一个对象</li>
  <li>接下来不断调用next对象，指针一直往后移动，直到指向最后一个成员</li>
  <li>每调用next方法，返回一个包含value和done的属性的对象</li>
</ol>

<p>应用场景：需要自定义遍历数据的时候，要想到迭代器</p>

<h5 id="自定义遍历数据">自定义遍历数据</h5>

<p>在 JavaScript 中，我们可以使用自定义迭代器来遍历数据。自定义迭代器是一个对象，它定义了一个 next() 方法，当调用 next() 方法时，它返回一个包含 value 和 done 属性的对象，value 属性表示当前迭代到的值，done 属性为 true 表示迭代结束，为 false 表示还有更多的值需要迭代</p>

<p>下面是一个简单的例子，展示了如何使用自定义迭代器来遍历一个数组：</p>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">myArray</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">];</span>

<span class="kd">const</span> <span class="nx">myIterator</span> <span class="o">=</span> <span class="p">{</span>
  <span class="p">[</span><span class="nb">Symbol</span><span class="p">.</span><span class="nx">iterator</span><span class="p">]:</span> <span class="kd">function</span><span class="p">()</span> <span class="p">{</span>
    <span class="kd">let</span> <span class="nx">index</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span>
    <span class="k">return</span> <span class="p">{</span>
      <span class="na">next</span><span class="p">:</span> <span class="kd">function</span><span class="p">()</span> <span class="p">{</span>
        <span class="k">if </span><span class="p">(</span><span class="nx">index</span> <span class="o">&lt;</span> <span class="nx">myArray</span><span class="p">.</span><span class="nx">length</span><span class="p">)</span> <span class="p">{</span>
          <span class="k">return</span> <span class="p">{</span> <span class="na">value</span><span class="p">:</span> <span class="nx">myArray</span><span class="p">[</span><span class="nx">index</span><span class="o">++</span><span class="p">],</span> <span class="na">done</span><span class="p">:</span> <span class="kc">false</span> <span class="p">};</span>
        <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
          <span class="k">return</span> <span class="p">{</span> <span class="na">done</span><span class="p">:</span> <span class="kc">true</span> <span class="p">};</span>
        <span class="p">}</span>
      <span class="p">}</span>
    <span class="p">};</span>
  <span class="p">}</span>
<span class="p">};</span>

<span class="k">for </span><span class="p">(</span><span class="kd">let</span> <span class="nx">value</span> <span class="k">of</span> <span class="nx">myIterator</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="nx">value</span><span class="p">);</span> <span class="c1">// 1 2 3</span>
<span class="p">}</span>
</code></pre></div></div>

<p>在这个例子中，我们定义了一个名为 myIterator 的对象，它包含一个 Symbol.iterator 方法，该方法返回一个包含 next() 方法的对象。next() 方法返回一个包含当前迭代到的值和是否迭代结束的对象。我们可以使用 for…of 循环来遍历 myIterator 对象，每次迭代时都会调用 next() 方法，直到 done 属性为 true 为止</p>

<h5 id="回调函数">回调函数</h5>

<p>回调函数是 JavaScript 中常见的一种编程模式，通常用于异步编程。一个回调函数就是一个函数，它作为参数传递给另一个函数，并且在该函数执行完毕后被调用。</p>

<p>以下是一个简单的例子，演示了如何使用回调函数来处理异步操作：</p>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">fetchData</span><span class="p">(</span><span class="nx">callback</span><span class="p">)</span> <span class="p">{</span>
  <span class="nf">setTimeout</span><span class="p">(()</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="kd">const</span> <span class="nx">data</span> <span class="o">=</span> <span class="p">{</span> <span class="na">name</span><span class="p">:</span> <span class="dl">"</span><span class="s2">John</span><span class="dl">"</span><span class="p">,</span> <span class="na">age</span><span class="p">:</span> <span class="mi">30</span> <span class="p">};</span>
    <span class="nf">callback</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
  <span class="p">},</span> <span class="mi">2000</span><span class="p">);</span>
<span class="p">}</span>

<span class="kd">function</span> <span class="nf">displayData</span><span class="p">(</span><span class="nx">data</span><span class="p">)</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="s2">`Name: </span><span class="p">${</span><span class="nx">data</span><span class="p">.</span><span class="nx">name</span><span class="p">}</span><span class="s2">, Age: </span><span class="p">${</span><span class="nx">data</span><span class="p">.</span><span class="nx">age</span><span class="p">}</span><span class="s2">`</span><span class="p">);</span>
<span class="p">}</span>

<span class="nf">fetchData</span><span class="p">(</span><span class="nx">displayData</span><span class="p">);</span>
</code></pre></div></div>

<p>这段代码的执行顺序和流程如下：</p>

<ol>
  <li>
    <p>首先，调用 <code class="language-plaintext highlighter-rouge">fetchData</code> 函数，并将 <code class="language-plaintext highlighter-rouge">displayData</code> 函数作为参数传递给它。</p>
  </li>
  <li>
    <p>在 <code class="language-plaintext highlighter-rouge">fetchData</code> 函数中，使用 <code class="language-plaintext highlighter-rouge">setTimeout</code> 函数模拟了一个异步操作，它会在 2000 毫秒后执行一个回调函数。</p>
  </li>
  <li>
    <p>在回调函数中，创建了一个包含 <code class="language-plaintext highlighter-rouge">name</code> 和 <code class="language-plaintext highlighter-rouge">age</code> 属性的对象，并将其作为参数传递给回调函数 <code class="language-plaintext highlighter-rouge">callback</code>。</p>
  </li>
  <li>
    <p><code class="language-plaintext highlighter-rouge">fetchData</code> 函数执行完毕后，控制权被返回到调用它的地方，即主程序。</p>
  </li>
  <li>
    <p>在主程序中，<code class="language-plaintext highlighter-rouge">fetchData</code> 函数的返回值为 <code class="language-plaintext highlighter-rouge">undefined</code>，因为它没有显式返回任何值。</p>
  </li>
  <li>
    <p>2000 毫秒后，<code class="language-plaintext highlighter-rouge">setTimeout</code> 函数执行回调函数。回调函数中调用了 <code class="language-plaintext highlighter-rouge">displayData</code> 函数，并将包含 <code class="language-plaintext highlighter-rouge">name</code> 和 <code class="language-plaintext highlighter-rouge">age</code> 属性的对象作为参数传递给它。</p>
  </li>
  <li>
    <p><code class="language-plaintext highlighter-rouge">displayData</code> 函数被调用，它会在控制台中输出 <code class="language-plaintext highlighter-rouge">Name: John, Age: 30</code>。</p>
  </li>
</ol>

<p>因此，整个程序的输出结果是 <code class="language-plaintext highlighter-rouge">Name: John, Age: 30</code>。</p>

<h4 id="12-promise">12. Promise</h4>

<h5 id="promise的定义和使用">Promise的定义和使用</h5>

<p><strong>Promise</strong>是ES6引入的异步编程的新解决方案，语法上Promise是一个构造函数，用来封装一部操作并可以获取其成功或失败的结果</p>

<p>一个<strong>Promise</strong>必然处于以下三种状态之一 ：</p>
<ul>
  <li>待定pending ：初始状态，既没有被兑现，也没有被拒绝</li>
  <li>已兑现fulfilled ：意味着操作成功完成</li>
  <li>已拒绝rejected ：意味着操作失败</li>
</ul>

<p><strong>Promise</strong>的使用 ：</p>
<ul>
  <li>Promise构造函数 new Promise((resolve,reject) =&gt; {})</li>
  <li>Promise.prototype.then()方法</li>
  <li>Promise.prototype.catch()方法</li>
</ul>

<p>当我们需要处理异步操作时，Promise 是一种非常有用的工具。Promise 是一个对象，它代表了一个异步操作的最终完成或失败，并且可以在完成或失败后返回一个值</p>

<p>下面是一个使用 Promise 的例子：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">const</span> <span class="nx">promise</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Promise</span><span class="p">((</span><span class="nx">resolve</span><span class="p">,</span> <span class="nx">reject</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="c1">// 异步操作</span>
  <span class="nf">setTimeout</span><span class="p">(()</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="kd">const</span> <span class="nx">randomNum</span> <span class="o">=</span> <span class="nb">Math</span><span class="p">.</span><span class="nf">random</span><span class="p">();</span>
    <span class="k">if </span><span class="p">(</span><span class="nx">randomNum</span> <span class="o">&gt;</span> <span class="mf">0.5</span><span class="p">)</span> <span class="p">{</span>
      <span class="nf">resolve</span><span class="p">(</span><span class="nx">randomNum</span><span class="p">);</span>
    <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
      <span class="nf">reject</span><span class="p">(</span><span class="dl">'</span><span class="s1">Random number is too low</span><span class="dl">'</span><span class="p">);</span>
    <span class="p">}</span>
  <span class="p">},</span> <span class="mi">1000</span><span class="p">);</span>
<span class="p">});</span>

<span class="nx">promise</span><span class="p">.</span><span class="nf">then</span><span class="p">((</span><span class="nx">result</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="s2">`The random number is </span><span class="p">${</span><span class="nx">result</span><span class="p">}</span><span class="s2">`</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
  <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="s2">`Error: </span><span class="p">${</span><span class="nx">error</span><span class="p">}</span><span class="s2">`</span><span class="p">);</span>
<span class="p">});</span>
</code></pre></div></div>

<p>在这个例子中，我们创建了一个 Promise 对象，并传入一个函数作为参数。这个函数接受两个参数：resolve 和 reject。当异步操作成功时，我们调用 resolve 函数并传入结果值。如果异步操作失败，则调用 reject 函数并传入错误信息</p>

<p>接着，我们使用 then 方法来处理 Promise 对象的成功情况，并使用 catch 方法来处理 Promise 对象的失败情况。在 then 方法中，我们可以访问异步操作的结果值，并进行一些处理。在 catch 方法中，我们可以访问异步操作的错误信息，并进行一些处理</p>

<p>在 JavaScript 中，我们可以使用 Promise 对象来封装 Ajax 请求。下面是一个简单的示例：</p>

<div class="language-js highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="kd">function</span> <span class="nf">ajax</span><span class="p">(</span><span class="nx">url</span><span class="p">)</span> <span class="p">{</span>
  <span class="k">return</span> <span class="k">new</span> <span class="nc">Promise</span><span class="p">(</span><span class="kd">function</span><span class="p">(</span><span class="nx">resolve</span><span class="p">,</span> <span class="nx">reject</span><span class="p">)</span> <span class="p">{</span>
    <span class="kd">var</span> <span class="nx">xhr</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">XMLHttpRequest</span><span class="p">();</span>
    <span class="nx">xhr</span><span class="p">.</span><span class="nf">open</span><span class="p">(</span><span class="dl">'</span><span class="s1">GET</span><span class="dl">'</span><span class="p">,</span> <span class="nx">url</span><span class="p">);</span>
    <span class="nx">xhr</span><span class="p">.</span><span class="nx">onload</span> <span class="o">=</span> <span class="kd">function</span><span class="p">()</span> <span class="p">{</span>
      <span class="k">if </span><span class="p">(</span><span class="nx">xhr</span><span class="p">.</span><span class="nx">status</span> <span class="o">===</span> <span class="mi">200</span><span class="p">)</span> <span class="p">{</span>
        <span class="nf">resolve</span><span class="p">(</span><span class="nx">xhr</span><span class="p">.</span><span class="nx">response</span><span class="p">);</span>
      <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
        <span class="nf">reject</span><span class="p">(</span><span class="nc">Error</span><span class="p">(</span><span class="nx">xhr</span><span class="p">.</span><span class="nx">statusText</span><span class="p">));</span>
      <span class="p">}</span>
    <span class="p">};</span>
    <span class="nx">xhr</span><span class="p">.</span><span class="nx">onerror</span> <span class="o">=</span> <span class="kd">function</span><span class="p">()</span> <span class="p">{</span>
      <span class="nf">reject</span><span class="p">(</span><span class="nc">Error</span><span class="p">(</span><span class="dl">'</span><span class="s1">Network Error</span><span class="dl">'</span><span class="p">));</span>
    <span class="p">};</span>
    <span class="nx">xhr</span><span class="p">.</span><span class="nf">send</span><span class="p">();</span>
  <span class="p">});</span>
<span class="p">}</span>

<span class="c1">// 调用示例</span>
<span class="nf">ajax</span><span class="p">(</span><span class="dl">'</span><span class="s1">https://jsonplaceholder.typicode.com/posts</span><span class="dl">'</span><span class="p">)</span>
  <span class="p">.</span><span class="nf">then</span><span class="p">(</span><span class="kd">function</span><span class="p">(</span><span class="nx">response</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="dl">'</span><span class="s1">成功获取到数据：</span><span class="dl">'</span><span class="p">,</span> <span class="nx">response</span><span class="p">);</span>
  <span class="p">})</span>
  <span class="p">.</span><span class="k">catch</span><span class="p">(</span><span class="kd">function</span><span class="p">(</span><span class="nx">error</span><span class="p">)</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nf">log</span><span class="p">(</span><span class="dl">'</span><span class="s1">获取数据失败：</span><span class="dl">'</span><span class="p">,</span> <span class="nx">error</span><span class="p">);</span>
  <span class="p">});</span>
  
</code></pre></div></div>

<p>在上面的示例中，我们定义了一个名为 ajax 的函数，它接受一个 URL 参数，并返回一个 Promise 对象。在 Promise 构造函数中，我们使用 XMLHttpRequest 对象来发送 Ajax 请求，并在请求成功或失败时调用 resolve 或 reject 方法来改变 Promise 的状态。在 then 方法中，我们定义了成功获取数据后的回调函数，而在 catch 方法中，我们定义了获取数据失败后的回调函数</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>