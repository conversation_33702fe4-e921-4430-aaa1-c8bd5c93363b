<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>好久不见-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="好久不见"/>
  <meta name="keywords" content="slince,trip"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="好久不见">好久不见</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2022-08-14
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#trip" class="hover-underline">trip</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>今天是很有意思的一天，我如约而至来到北京会见了两个好朋友A和B，还有A的对象C。</p>

<p>这次地行程如果用一个词来形容的话，那就是神奇！</p>

<p>上午的时候，我先是和好朋友B碰面，我俩相约一起去国家博物馆，有意思的地方来了，我反应了半天才看到她，感觉自己今天脑子完全不在线，好几次差点做错地铁走错路😂 。 她穿着一身黄裙，真的好好看，我这个笨脑袋都忘记夸人家了，该打该打！</p>

<p>随后我二人来到博物馆进行观览，由于时间有限还有其他安排，我们也只是看了古代馆，有一说一值得表扬的地方就是所有古屋是按照时间线来展示的，每个朝代有着属于它的特色和标志性的物品。看到了不少很有回味的物件，像一些青铜镜，上面的花纹还有刻印的字迹都十分的生动，那上面的纹路真的很棒！还有一些古代农业用的灌溉器具、兵器、乐器，不由得想到了诗书礼乐，很大一种程度上感觉古人在这方面甚至比现代人做的还要好，研究的还要精通。</p>

<p>期间帮她拍了几张照片，嗯自认为感觉还不错，希望她会喜欢，哈哈哈。她真的不像自己说的那样，她真的很上镜很上镜，以后有机会一起拍一组艺术写真照吧。</p>

<p>不知不觉时间过去了大半，我们浅浅逛完了夏商周的一些古物就出来了，最后看到了四羊方尊，（大是真的大，纹路真的是太太太好看了，如果有小一点的纺织品的话，我一定买回来当水杯用）不过去没看到司母戊鼎，有点小可惜了。</p>

<p>四十多分的地铁路程之后，我们抵达了目的地，京门老爆三，一家火锅店，运气还不错，有一个靠窗的位置，我们刚坐下不久，好友A带着她对象C（下文我称他大哥）就过来了，至此我四人终于齐聚！</p>

<p>百闻不如一见，10分满分，我能给8分，长得确实很好看，而且看起来也很成熟，聊起来给人的感觉不仅成熟也很温和，真想不到这个A运气咋这好！有这么好一男朋友，我跟B我俩吐槽也就大哥能受了你这性格，哈哈哈。</p>

<p>真好，我和B俩人看着他们两个，共同想说的就是“虽然不易，但真的真的希望你们能走到最后，要你俩都那啥了，我们就真不相信爱情了。”</p>

<p>午餐过后我们找了一家星巴克点一些喝的东西，然后在那里休息了一会儿。我们聊聊这个聊那个还是很有趣的，歇息了一会之后，我就说我们真的要感谢一下我们四个共同的大学，真的真的真的，真的就是缘分吧，不然怎么可能有机会一起坐下来在这里聊天，还成为了好朋友。如果以后还有机会的话，我还是想和他们三个人一起坐在一家餐厅或者一家小酒馆，再次说说笑笑。</p>

<p>有个小插曲，哈哈哈，我们四个随后在这个商场里逛了逛，最后来到了一家玩具店？可以买到毛绒玩具的地方，这时候我觉得自己还算有点脑子，帮B选了一只小老虎😂，手感非常之不错。本来本来他们三个我是都有买礼物的，只是浙江那边疫情了，快递发不过来了还是临时被通知的，这就有点尴尬了，所以大哥你俩别怪我。</p>

<p>晚上简单喝了点小米粥就踏上了归程。</p>

<p>好家伙，直接来了一波生死时速😂 7:30买票，7:34上电梯，7:36找到检票口，7:38坐下，7:39发车！
老天开眼，今天运气变好了，我没来得及看车站的电子屏幕，所以一开始根本是乱找检票口，但是但是，我刚上电梯往前面走了几个站口就看到了我那趟车次的检票口。最终坐上了回去的高铁！奈斯！！</p>

<p>最后的最后，说一句最俗的话，“希望我们平安喜乐。”</p>


  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>