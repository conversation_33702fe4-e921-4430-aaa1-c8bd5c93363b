<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>古都洛阳-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="古都洛阳"/>
  <meta name="keywords" content="slince,trip"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906135328">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906135328">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906135328">
  <link rel="prefetch" href="/static/js/search.js?t=20250906135328">
  <script src="/static/js/translations.js?t=20250906135328" defer></script>
  <script src="/static/js/language.js?t=20250906135328" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906135328",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <img class="logo" src="/static/img/logo.jpg" alt="logo"/>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="古都洛阳">古都洛阳</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2022-06-20
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#trip" class="hover-underline">trip</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <h1 id="古都洛阳">古都洛阳</h1>

<p>2022年6月14号凌晨，廊坊的行程码终于不带星星了，终于，这座城市变成低风险地区了，这也就意味着，之前计划好久洛阳的旅途可以开始了。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/4sUiWdOO.png" alt="IMG_5176.png" />
出发前一天的傍晚</p>

<p>6月15号上午十点多我坐上了去往洛阳的列车，时隔多年，第一次坐这么久的火车，属实是有些不适应。
<img src="https://img1.imgtp.com/2022/06/20/MstzXvGj.png" alt="IMG_5181-2.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/jkKT4BbL.jpg" alt="IMG_5184.jpg" /></p>

<p>旅途开始了。
饿，这着实有些令人想不到，火车一经发动手机信号就一直很差，导致我中间和朋友联系的时候时常没有信号和网络。好在，我带了一本书《达芬奇密码》，路上还可以解解闷，手机就变成了无脑的拍照机器了。</p>

<p>这趟火车挺有意思的，坐在我对面的大叔是要坐到这趟列车的终点站，昆明。好家伙，这得坐三天两夜了！大叔说，来的时候坐的飞机只有几百块，现在回家的话坐飞机得一千多了，坐火车能省好几百。我心里想想，没准这也会是我以后的常态，为了剩下几百块钱选择不那么舒服的绿皮火车。</p>

<p>到达保定站的时候，又上来一位大叔，这回是坐我旁边，我的位置靠窗，起初没太注意，只是我在看书或者打瞌睡的时候，大叔嘴里一直念叨着什么，而且声音还很大。每当乘务员推着卖吃的东西的小推车过来的时候，他会买一瓶酒，emm咱就说味道真的很大。我也听不懂他在说什么，总之挺难沟通的。</p>

<p>倒是下一站，上来一对夫妇，带着个小孩，很可爱，他们要去贵阳，听他们说要回女方的家，好长时间没回去过了，带着孩子回去一次。有趣的事情来了，坐我旁边的大叔似乎很喜欢这个小孩子，一直在逗他，虽然我并不觉得小孩有被他逗到。后来听他说，是因为这个小孩跟他家里的孩子很像，孙子或者孙女吧，大叔看着他就想到了自己家的孩子。因为这个小孩子的出现，给了这些互不认识的陌生人一个可以沟通的桥梁。</p>

<p>我并未介入他们的对话，只是作为一个暂时的同路人倾听着他们的对话……</p>

<p>好长好长好长时间后（十多小时）终于抵达了目的地—洛阳关林站。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/DOje9Xr1.png" alt="tempImageJfhEOX.png" /></p>

<p>来这里之后的第一顿，芜湖，虽然不是很饿了，但是这些小吃都十分的美味，而且很有特色，虽然我想不起来叫什么了哈哈哈哈，最喜欢的还是海碧（洛阳当地自己生产的饮料 ），海碧yyds！！</p>

<p>她把我送回酒店后，我洗洗睡了过去。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/mz6ZkXPW.png" alt="tempImageUyqHDb.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/BQ1RjoTf.png" alt="tempImage4G7EM5.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/7r2DwlZp.png" alt="tempImageDPgl2P.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/jHlP2a3c.png" alt="tempImageEcTxMO.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/kDOsNTNd.png" alt="tempImageGhjy6s.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/plwlqIPk.png" alt="tempImageHqBhgi.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/NA7lcavn.png" alt="tempImageKKuk1j.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/ZMVGMyp3.png" alt="tempImageUg6zDE.png" /></p>

<p>然后我们浅浅逛了一下博物馆，嗯“浅浅”，脚会痛的那种。不过有一说一，有很多很好看的文物，真的很吸引。最喜欢的是白玉杯，很纯粹的玉透着淡淡的光泽，我想象不出古人是如何烧制出来的，如果仅仅是烧制一个玉杯那很容易，可玉杯上面的纹路又是运用了什么样的工艺……</p>

<p>晚上去了十字街，好家伙是真的好家伙。人好多巨多！路两边都是卖各种吃的，emmm叫不上名字了已经哈哈哈哈哈，不过都超级好吃！</p>

<p>第三天下午去了商场、浅浅逛了一下，我买了件无袖白色上衣，她买了两个吊带，蛮适合她的。
<img src="https://img1.imgtp.com/2022/06/20/ZMVGMyp3.png" alt="tempImageUg6zDE.png" /></p>

<p>还有憨批的二柱子哈哈哈哈</p>

<p><img src="https://img1.imgtp.com/2022/06/20/kUx9pTR8.png" alt="tempImage0KYaBV.png" /></p>

<p>晚上去了洛邑古城，芜湖，小巷的感觉十分棒，还喝了很好喝的丸子汤！到这里就能看到巨多的穿汉服的小姐姐。
<img src="https://img1.imgtp.com/2022/06/20/f8nJ3APX.png" alt="tempImageu2QQfr.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/cLFkvciN.png" alt="tempImageL2C4gA.png" />
随着天色渐渐暗下来，古城的黄晕色光缓缓亮起，这座古城似乎又活了过来，一呼一吸，一呼一吸，两个感受着这座古城的脉动。</p>

<p>晚上送她回家后我骑车回酒店，再次好家伙，迷路了😂，真的不怪我天太黑了，废了好大劲才找到另外一条回酒店的路，回去跟她说我迷路了，给她笑死。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/47SAefPn.png" alt="tempImage1YQmJd.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/3MaFkDiS.png" alt="tempImage4x10rE.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/J4tvRkjl.png" alt="tempImagejeZiZg.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/lvpiwAFt.png" alt="tempImageKmcDWD.png" /></p>

<p>第四天，去了白马寺，跟随她的步伐一起祭拜了一下，求个平安，亲人朋友们都好好的。</p>

<p>不过那个止语茶舍很有意思，如同字面，大家进去只是喝茶歇息，人们也只是轻轻小声交流，生怕惊扰了这寺庙中的禅僧。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/deDFmds2.png" alt="tempImagevmN6po.png" /></p>

<p>走之前终于看到了寺猫，它好慵懒哈哈哈哈哈，轻轻让我摸了摸就跑去晒太阳的，不过它身上的味道好香，可能是因为这所寺庙的缘故吧。</p>

<p><img src="https://img1.imgtp.com/2022/06/20/BBhs889u.png" alt="tempImageD5k437.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/tclHMCiS.png" alt="tempImagesQ4bun.png" /></p>

<p><img src="https://img1.imgtp.com/2022/06/20/f49ogL88.png" alt="tempImagezuXVgR.png" /></p>

<p>晚上来到了应天门，本来还想去什么什么公园，又给忘了，这个地名确实不太好记哈。</p>

<p>我们浅浅逛了一下，然后去吃了点东西，送她回家后我回酒店休息，小眯一会后坐上了凌晨三点的火车。
也不知道写了多少字，写了一个小时多点，外面的天也慢慢亮了，刚刚还看到了浅红紫颜色的朝霞，这趟旅途也就到此结束了。</p>

<p>下次再来不知又是什么时候了，总之长路漫漫，我也愿做一个苦修去往下一个站点。</p>

<p>咱就说长途旅行好累…….</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906135328"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906135328"></script>

</body>
</html>