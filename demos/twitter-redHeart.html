<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Twitter Red Heart</title>
    <style>
      .redHeart {
        background-image: url('../posts/2025/01/25/01.png');
        width: 100px;
        height: 100px;
        /* 这里设置 2900px 的缘故是因为精灵图，分割的话有 29 张小图 */
        background-size: 2900px 100px;
        /* jump-none 确保动画在每一步之间没有平滑过渡 */
        animation: redHeart 2s steps(29, jump-none) infinite;
      }
      @keyframes redHeart {
        0% {
          /* 第一张小图 */
          background-position: 0px 0px;
        }
        100% {
          /* 最后一张小图，负代表左滑动 */
          background-position: -2800px 0px;
        }
      }
    </style>
  </head>
  <body>
    <!-- <img
      src="../posts/2025/01/25/01.png"
      alt="redHeart" /> -->
    <div class="redHeart"></div>
  </body>
</html>
